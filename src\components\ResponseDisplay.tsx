import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Copy, ThumbsUp, ThumbsDown, MessageSquare } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { IntentParseResponse, SalesAgentResponse } from '@/types/api';
import { getSourceColor } from '@/lib/utils';

interface ResponseDisplayProps {
  content: IntentParseResponse | SalesAgentResponse;
  messageId: string;
  requestId?: string;
  feedback?: {
    helpful: boolean | null;
    comment?: string;
  };
  onFeedbackChange: (feedback: { helpful: boolean | null; comment?: string }) => void;
  type: 'intent' | 'sales';
}

const ResponseDisplay: React.FC<ResponseDisplayProps> = ({
  content,
  messageId,
  requestId,
  feedback,
  onFeedbackChange,
  type
}) => {
  const [activeTab, setActiveTab] = useState('visual');
  const [feedbackComment, setFeedbackComment] = useState(feedback?.comment || '');
  const [showCommentField, setShowCommentField] = useState(false);
  const { toast } = useToast();

  const copyRequestId = () => {
    if (requestId) {
      navigator.clipboard.writeText(requestId);
      toast({
        title: "Copied!",
        description: "Request ID copied to clipboard",
        duration: 2000,
      });
    }
  };

  const handleFeedback = (helpful: boolean) => {
    const newFeedback = feedback?.helpful === helpful ? null : helpful;
    onFeedbackChange({ helpful: newFeedback, comment: feedbackComment });
    
    if (newFeedback !== null) {
      setShowCommentField(true);
    }
  };

  const handleCommentSave = () => {
    onFeedbackChange({ helpful: feedback?.helpful || null, comment: feedbackComment });
    setShowCommentField(false);
    toast({
      title: "Feedback saved",
      description: "Thank you for your feedback!",
      duration: 2000,
    });
  };

  const renderIntentResponse = (response: IntentParseResponse) => (
    <div className="text-sm space-y-3">
      <div className="grid grid-cols-2 gap-3">
        <div>
          <span className="font-semibold text-gray-700">Intent:</span>
          <div className="text-teal-600 font-medium">{response.intent}</div>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Confidence:</span>
          <div className="text-green-600 font-medium">
            {(response.confidence * 100).toFixed(1)}%
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <div>
          <span className="font-semibold text-gray-700">Source:</span>
          <div className={`font-medium ${getSourceColor(response.source).split(' ')[0]}`}>
            {response.source}
          </div>
        </div>
        <div>
          <span className="font-semibold text-gray-700">Processing Time:</span>
          <div className="text-blue-600 font-medium">
            {response.processing_time_ms.toFixed(5)}ms
          </div>
        </div>
      </div>

      <div>
        <span className="font-semibold text-gray-700">Entities:</span>
        <div className="mt-1">
          {response.entities && Object.keys(response.entities).length > 0 ? (
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(response.entities, null, 2)}
            </pre>
          ) : (
            <span className="text-gray-500 italic">None detected</span>
          )}
        </div>
      </div>

      {response.pattern_matched && (
        <div>
          <span className="font-semibold text-gray-700">Pattern Matched:</span>
          <div className="text-gray-600 mt-1 text-xs font-mono bg-gray-100 p-1 rounded">
            {response.pattern_matched}
          </div>
        </div>
      )}

      {response.rule_id && (
        <div>
          <span className="font-semibold text-gray-700">Rule:</span>
          <div className="text-gray-600 mt-1">
            {response.rule_id} ({response.rule_source})
          </div>
        </div>
      )}
    </div>
  );

  const renderSalesResponse = (response: SalesAgentResponse) => (
    <div className="text-sm">
      <div className="whitespace-pre-wrap">
        {response.content.content}
      </div>
    </div>
  );

  return (
    <div className="space-y-3">
      {/* Request ID */}
      {requestId && (
        <div className="flex items-center justify-between text-xs text-gray-500 border-b pb-2">
          <span className="font-mono">Request ID: {requestId}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={copyRequestId}
            className="h-6 px-2 text-xs"
          >
            <Copy className="w-3 h-3 mr-1" />
            Copy
          </Button>
        </div>
      )}

      {/* Tabbed Response Display */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 h-8">
          <TabsTrigger value="visual" className="text-xs">Visual View</TabsTrigger>
          <TabsTrigger value="raw" className="text-xs">Raw JSON</TabsTrigger>
        </TabsList>
        
        <TabsContent value="visual" className="mt-3">
          {type === 'intent' 
            ? renderIntentResponse(content as IntentParseResponse)
            : renderSalesResponse(content as SalesAgentResponse)
          }
        </TabsContent>
        
        <TabsContent value="raw" className="mt-3">
          <div className="relative">
            <div className="absolute top-2 right-5 z-10">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(content, null, 2));
                  toast({
                    title: "Copied!",
                    description: "JSON response copied to clipboard",
                    duration: 2000,
                  });
                }}
                className="h-7 px-2 text-xs bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy JSON
              </Button>
            </div>
            <pre className="text-xs bg-gray-900 text-green-400 p-3 rounded overflow-auto max-h-60 pr-20">
              {JSON.stringify(content, null, 2)}
            </pre>
          </div>
        </TabsContent>
      </Tabs>

      {/* Feedback System */}
      <div className="border-t pt-3 space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-600">Was this response helpful?</span>
          <Button
            variant={feedback?.helpful === true ? "default" : "outline"}
            size="sm"
            onClick={() => handleFeedback(true)}
            className="h-7 px-2 text-xs"
          >
            <ThumbsUp className="w-3 h-3 mr-1" />
            Helpful
          </Button>
          <Button
            variant={feedback?.helpful === false ? "destructive" : "outline"}
            size="sm"
            onClick={() => handleFeedback(false)}
            className="h-7 px-2 text-xs"
          >
            <ThumbsDown className="w-3 h-3 mr-1" />
            Not Helpful
          </Button>
        </div>

        {showCommentField && (
          <div className="space-y-2">
            <Textarea
              placeholder="Optional: Add feedback comments..."
              value={feedbackComment}
              onChange={(e) => setFeedbackComment(e.target.value)}
              className="text-xs min-h-[60px]"
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleCommentSave} className="h-7 text-xs">
                Save Feedback
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowCommentField(false)}
                className="h-7 text-xs"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {feedback?.comment && !showCommentField && (
          <div className="text-xs bg-gray-50 p-2 rounded">
            <div className="flex items-center gap-1 mb-1">
              <MessageSquare className="w-3 h-3" />
              <span className="font-medium">Your feedback:</span>
            </div>
            <p className="text-gray-600">{feedback.comment}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponseDisplay;
