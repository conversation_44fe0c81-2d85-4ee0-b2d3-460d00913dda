
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Activity,Play, Search, ShoppingCart, Star, CreditCard, CalendarClock, Mic, BarChart3, Sparkles } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const DemoSection = () => {
  const demos = [
    {
      title: "Conversational Sales Assistant",
      emoji: "🧠",
      description: "From product image to personalized offer — Briskk sells like your best rep, right inside WhatsApp.",
      features: [
        { icon: <Search className="w-5 h-5 text-teal-600" />, text: "Visual search from customer image" },
        { icon: <ShoppingCart className="w-5 h-5 text-teal-600" />, text: "Personalized product recommendation" },
        { icon: <Sparkles className="w-5 h-5 text-teal-600" />, text: "Smart nudging — like a great in-store salesperson" },
        { icon: <Star className="w-5 h-5 text-teal-600" />, text: "Loyalty points + offer unlocks" },
        { icon: <CreditCard className="w-5 h-5 text-teal-600" />, text: "One-tap, seamless checkout in WhatsApp" }
      ],      
      outcome: "+17% Conversion Rate · 2× Average Order Value",
      reversed: false,
      gif:"/conv_ai.gif",
      explanation:"It works Under your Whatsapp Business Account"
    },
    {
      title: "Insightful Store Analyst",
      emoji: "📊",
      description: 'From "how many orders today?" to instant answers — Briskk replaces dashboards with conversation.',
      features: [
        { icon: <Mic className="w-5 h-5 text-teal-600" />, text: "Voice/text input from store staff" },
        { icon: <BarChart3 className="w-5 h-5 text-teal-600" />, text: "Sales & SKU insights inside WhatsApp" },
        { icon: <Activity className="w-5 h-5 text-teal-600" />, text: "Live stock tracking & restock prompts" },
        { icon: <CalendarClock className="w-5 h-5 text-teal-600" />, text: "Forecasting alerts (coming soon)" }
      ],      
      outcome: "5× Faster Insights · –30% Inventory Mismatch",
      reversed: true,
      gif:"analytics_ai.gif",
      explanation:"It works Under your Briskk's Business Account"
    }
  ];

  return (
    <section id="demos" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4">
            Briskk’s AI Agents, in Action.
          </h2>
          <p className="text-center text-gray-500 max-w-xl mx-auto mt-2 text-sm">
          See how they sell, support, and scale — all through WhatsApp.
        </p>
        </motion.div>
        
        <div className="space-y-16">
          {demos.map((demo, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              className={`flex flex-col ${demo.reversed ? 'md:flex-row-reverse' : 'md:flex-row'} gap-8 items-center`}
            >
              {/* Video/Demo Side */}
              <div className="w-full md:w-1/2">
                <div className="relative bg-white rounded-xl overflow-hidden h-[300px] md:h-[400px] shadow-md flex items-center justify-center">
                  <img
                    src={demo.gif} // path inside your /public folder
                    alt="Conversational Sales Assistant Demo"
                    className="h-full w-auto object-contain"
                  />
                </div>
              </div>
              
              {/* Content Side */}
              <div className="w-full md:w-1/2">
                <h3 className="text-2xl md:text-3xl font-bold mb-3 flex items-center">
                  <span className="mr-2">{demo.emoji}</span> {demo.title}
                </h3>
                
                <p className="text-lg text-gray-700 mb-6">
                  {demo.description}
                </p>
                
                <div className="mb-6">
                  <h4 className="text-sm font-semibold uppercase text-gray-500 mb-4">{demo.explanation}</h4>
                  <div className="space-y-3">
                    {demo.features.map((feature, i) => (
                      <div key={i} className="flex items-center">
                        {feature.icon}
                        <span className="ml-3 text-gray-700">{feature.text}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Badge variant="outline" className="px-4 py-1.5 text-sm bg-teal-50 border-teal-200 text-teal-800 font-medium">
                  ✅ {demo.outcome}
                </Badge>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DemoSection;
