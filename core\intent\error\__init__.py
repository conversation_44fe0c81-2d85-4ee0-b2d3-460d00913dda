"""
Error tracking package for intent parsing.

This package provides comprehensive error tracking, analysis, and
management capabilities for the intent parsing system.
"""

from .error_types import <PERSON>rrorEvent, ErrorCategory, ErrorSeverity
from .error_collector import <PERSON>rrorCollector, get_error_collector
from .error_analyzer import <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>, get_error_analyzer

# Convenience function for tracking errors
def track_intent_error(
    category: ErrorCategory,
    severity: ErrorSeverity,
    component: str,
    error: Exception,
    **kwargs
) -> str:
    """
    Convenience function to track intent parsing errors.
    
    Args:
        category: Error category
        severity: Error severity
        component: Component where error occurred
        error: The exception that occurred
        **kwargs: Additional context
        
    Returns:
        Error event ID
    """
    error_event = ErrorEvent.create(
        category, severity, component, error, **kwargs
    )
    return get_error_collector().collect_error(error_event)

__all__ = [
    "ErrorEvent",
    "ErrorCategory", 
    "ErrorSeverity",
    "ErrorCollector",
    "ErrorAnalyzer",
    "get_error_collector",
    "get_error_analyzer",
    "track_intent_error",
]
