"""
Legacy error handlers interface for backward compatibility.

This module provides a legacy interface that delegates to the new
modular error handling system.
"""

import logging
from typing import Dict, Optional, Any, Callable

from .error import (
    ErrorCategory, ErrorSeverity, track_intent_error
)
from .error.handlers import (
    <PERSON><PERSON>nent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    LLMError<PERSON>andler, create_error_handler, handle_component_error
)

logger = logging.getLogger(__name__)


# Legacy classes that delegate to new modular handlers
# These maintain backward compatibility

class ClarifierErrorHandler(ComponentErrorHandler):
    """Legacy clarifier error handler - delegates to new system."""

    def __init__(self, error_tracker=None):
        super().__init__("clarifier", error_tracker)

class OrchestrationErrorHandler(ComponentErrorHandler):
    """Legacy orchestration error handler - delegates to new system."""

    def __init__(self, error_tracker=None):
        super().__init__("orchestration", error_tracker)


def track_and_log_error(
    component: str,
    error: Exception,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    **kwargs
) -> str:
    """Track and log an error with appropriate categorization."""
    # Determine category from component name
    component_lower = component.lower()
    if "rule" in component_lower:
        category = ErrorCategory.RULE_ENGINE
    elif "cache" in component_lower:
        category = ErrorCategory.CACHE
    elif "llm" in component_lower:
        category = ErrorCategory.LLM
    elif "clarifier" in component_lower:
        category = ErrorCategory.CLARIFIER
    elif "session" in component_lower:
        category = ErrorCategory.SESSION
    elif "orchestrat" in component_lower:
        category = ErrorCategory.ORCHESTRATION
    else:
        category = ErrorCategory.UNKNOWN

    return track_intent_error(category, severity, component, error, **kwargs)
