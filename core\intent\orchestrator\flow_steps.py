"""
Flow step coordinator for intent parsing orchestration.

This module coordinates the individual step implementations and provides
a unified interface for the orchestration flow.
"""

import logging
from typing import Dict, Optional, Any, List

from .steps import RuleEngineStep, CacheStep, LLMStep, ClarifierStep
from ..response import ParseResponseBuilder
from ..intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class FlowStepCoordinator:
    """Coordinates individual flow steps for the orchestration."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

        # Initialize step handlers
        self.rule_step = RuleEngineStep(orchestrator)
        self.cache_step = CacheStep(orchestrator)
        self.llm_step = LLMStep(orchestrator)
        self.clarifier_step = ClarifierStep(orchestrator)

    def execute_rule_step(
        self,
        message: str,
        tenant_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """Execute rule engine step."""
        return self.rule_step.execute(
            message, tenant_id, locale, metadata, response_builder, source_chain
        )

    def execute_cache_step(
        self,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """Execute cache lookup step."""
        return self.cache_step.execute(
            message, tenant_id, metadata, response_builder, source_chain
        )


    def execute_llm_step(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
        session_context: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Execute LLM parsing step."""
        return self.llm_step.execute(
            message, tenant_id, session_id, locale, metadata,
            response_builder, source_chain, user_id, session_context
        )

    def execute_clarifier_step(
        self,
        initial_result: Dict[str, Any],
        session_id: Optional[str],
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
    ) -> Dict[str, Any]:
        """Execute clarifier step."""
        return self.clarifier_step.execute(
            initial_result, session_id, tenant_id, metadata, response_builder
        )

    def validate_all_steps(self) -> Dict[str, Any]:
        """
        Validate all step components.

        Returns:
            Dictionary with validation results for all steps
        """
        return {
            "rule_step": self.rule_step.validate_rule_engine(),
            "cache_step": self.cache_step.validate_cache(),
            "llm_step": self.llm_step.validate_llm(),
            "clarifier_step": self.clarifier_step.validate_clarifier(),
        }

    def get_debug_info_all_steps(
        self,
        message: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get debug information for all steps.

        Args:
            message: Test message
            tenant_id: Tenant identifier

        Returns:
            Dictionary with debug information for all steps
        """
        return {
            "rule_step": self.rule_step.get_debug_info(message, tenant_id),
            "cache_step": self.cache_step.get_debug_info(message, tenant_id),
            "llm_step": self.llm_step.get_debug_info(message, tenant_id),
            "clarifier_step": self.clarifier_step.get_debug_info(),
        }


