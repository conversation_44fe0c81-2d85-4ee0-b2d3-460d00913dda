import { TestSession, ChatMessage } from '@/types/api';

/**
 * Generate a unique session ID using crypto.randomUUID() with fallback
 */
export const generateSessionId = (): string => {
  // Primary method: crypto.randomUUID() for modern browsers
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // Fallback for older browsers
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * Generate a unique message ID
 */
export const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Create a new test session
 */
export const createTestSession = (): TestSession => {
  return {
    id: generateSessionId(),
    created_at: new Date(),
    messages: [
      {
        id: generateMessageId(),
        type: 'system',
        content: 'Debug session started. You can now test the AI agent functionality.',
        timestamp: new Date(),
      }
    ],
  };
};

/**
 * Add a message to a session
 */
export const addMessageToSession = (
  session: TestSession, 
  message: Omit<ChatMessage, 'id' | 'timestamp'>
): TestSession => {
  const newMessage: ChatMessage = {
    ...message,
    id: generateMessageId(),
    timestamp: new Date(),
  };

  return {
    ...session,
    messages: [...session.messages, newMessage],
  };
};

/**
 * Reset session while preserving the session ID
 */
export const resetSession = (session: TestSession): TestSession => {
  return {
    ...session,
    created_at: new Date(),
    messages: [
      {
        id: generateMessageId(),
        type: 'system',
        content: 'Session reset. Previous conversation cleared.',
        timestamp: new Date(),
      }
    ],
  };
};

/**
 * Format timestamp for display
 */
export const formatTimestamp = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

/**
 * Calculate session duration
 */
export const getSessionDuration = (session: TestSession): string => {
  const now = new Date();
  const duration = now.getTime() - session.created_at.getTime();
  
  const minutes = Math.floor(duration / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
};

/**
 * Get the last user message from session
 */
export const getLastUserMessage = (session: TestSession): ChatMessage | null => {
  const userMessages = session.messages.filter(msg => msg.type === 'user');
  return userMessages.length > 0 ? userMessages[userMessages.length - 1] : null;
};

/**
 * Count messages by type
 */
export const getMessageStats = (session: TestSession) => {
  const stats = {
    total: session.messages.length,
    user: 0,
    agent: 0,
    system: 0,
  };

  session.messages.forEach(msg => {
    stats[msg.type]++;
  });

  return stats;
};

/**
 * Generate a unique request ID
 */
export const generateRequestId = (): string => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback for older browsers
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Download session data as JSON file
 */
export const downloadSessionLogs = (
  intentSession: TestSession,
  salesSession: TestSession,
  filename?: string
): void => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const defaultFilename = `debug-session-${intentSession.id.split('-')[0]}-${timestamp}.json`;

  const exportData = {
    export_info: {
      exported_at: new Date().toISOString(),
      version: '1.0.0',
      type: 'ai_agent_debug_session'
    },
    sessions: {
      intent_parser: {
        ...intentSession,
        stats: getMessageStats(intentSession),
        duration: getSessionDuration(intentSession)
      },
      sales_agent: {
        ...salesSession,
        stats: getMessageStats(salesSession),
        duration: getSessionDuration(salesSession)
      }
    },
    summary: {
      total_messages: intentSession.messages.length + salesSession.messages.length,
      intent_parser_messages: getMessageStats(intentSession),
      sales_agent_messages: getMessageStats(salesSession),
      session_start: new Date(Math.min(
        intentSession.created_at.getTime(),
        salesSession.created_at.getTime()
      )).toISOString(),
      export_timestamp: timestamp
    }
  };

  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  });

  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || defaultFilename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
