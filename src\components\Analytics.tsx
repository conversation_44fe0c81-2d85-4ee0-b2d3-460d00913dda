import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const Analytics = () => {
  const location = useLocation();

  useEffect(() => {
    const trackingId = import.meta.env.VITE_GA_TRACKING_ID;

    if (!trackingId) return;

    // Load GA script once
    if (!window.gtag) {
      const script = document.createElement("script");
      script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
      script.async = true;
      document.head.appendChild(script);

      const inlineScript = document.createElement("script");
      inlineScript.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
      `;
      document.head.appendChild(inlineScript);
    }

    // Push page view on route change
    window.gtag?.("config", trackingId, {
      page_path: location.pathname,
    });
  }, [location.pathname]);

  return null;
};

export default Analytics;
