"""
Rule engine step implementation for intent parsing orchestration.

This module contains the rule engine step logic that handles
regex-based intent matching.
"""

import time
import logging
from typing import Dict, Optional, Any, List

from ...intent_utils import (
    log_parsing_result,
    log_general_event,
    calculate_processing_time,
)
from ...response import ParseResponseBuilder
from ...intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class RuleEngineStep:
    """Handles rule engine step in the orchestration flow."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def execute(
        self,
        message: str,
        tenant_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Execute rule engine step.

        Args:
            message: User message
            tenant_id: Tenant identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Rule match result or None if no match
        """
        rule_start = time.perf_counter()
        source_chain.append("rule")
        metadata.add_source("rule")

        try:
            # 🔍 INTENTIONAL ERROR FOR TESTING COMPONENT-LEVEL ERROR TRACKING
            # This demonstrates how errors in specific step modules are tracked
            if message == "test_rule_step_error":
                raise ValueError(f"INTENTIONAL RULE STEP ERROR in rule_step.py:execute:line_61 - Testing component-level error tracking in rule engine step module (204 lines)")

            rule_match = self.orchestrator.rule_engine.match(message, tenant_id, locale)
            metadata.rule_check_time_ms = calculate_processing_time(rule_start)

            if rule_match:
                return self._handle_rule_match(
                    rule_match, message, tenant_id, metadata,
                    response_builder, source_chain
                )
            else:
                log_general_event("rule_no_match", message=message[:50])
                return None

        except Exception as e:
            return self._handle_rule_error(e, rule_start, metadata, message)

    def _handle_rule_match(
        self,
        rule_match,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Dict[str, Any]:
        """Handle successful rule match."""
        metadata.rule_matched = True

        # Build response
        result = response_builder.build_rule_response(
            rule_match, metadata.rule_check_time_ms, source_chain
        )

        # Cache successful rule matches for faster future lookups
        self.orchestrator.query_cache.set(message, result, tenant_id)

        # Update source chain to show caching occurred
        if len(source_chain) > 0 and source_chain[-1] == "rule":
            source_chain[-1] = "rule → cache"

        # Log with reasoning for debugging - Rule Reasoning Debug INTEGRATED
        logger.debug(f"Rule match reasoning: {getattr(rule_match, 'reasoning', 'No reasoning available')}")
        log_parsing_result(
            "rule_match", rule_match.intent, rule_match.confidence
        )
        return result

    def _handle_rule_error(
        self,
        error: Exception,
        rule_start: float,
        metadata: ParseMetadata,
        message: str,
    ) -> None:
        """Handle rule engine errors."""
        metadata.add_error(f"Rule engine failed: {str(error)}")
        metadata.rule_check_time_ms = calculate_processing_time(rule_start)
        log_general_event("rule_error", error=str(error))
        logger.error(f"Rule engine step failed: {error}")
        return None

    def validate_rule_engine(self) -> Dict[str, Any]:
        """
        Validate rule engine state.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": {}
        }

        try:
            # Check if rule engine is available
            if not self.orchestrator.rule_engine:
                validation["errors"].append("Rule engine not initialized")
                validation["valid"] = False
                return validation

            # Check rule count
            rule_count = self.orchestrator.rule_engine.get_rule_count()
            validation["info"]["rule_count"] = rule_count

            if rule_count == 0:
                validation["warnings"].append("No rules loaded")

            # Validate rules
            rule_validation = self.orchestrator.rule_engine.validate_rules()
            validation["info"]["rule_validation"] = rule_validation

            if not rule_validation["valid"]:
                validation["errors"].extend(rule_validation["errors"])
                validation["valid"] = False

        except Exception as e:
            validation["errors"].append(f"Rule engine validation failed: {e}")
            validation["valid"] = False

        return validation

    def get_debug_info(self, message: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get debug information for rule engine step.
        
        Args:
            message: Test message
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with debug information
        """
        debug_info = {
            "step": "rule_engine",
            "rule_engine_available": self.orchestrator.rule_engine is not None,
            "validation": self.validate_rule_engine(),
        }

        if self.orchestrator.rule_engine and message:
            try:
                # Test rule matching
                start_time = time.perf_counter()
                rule_match = self.orchestrator.rule_engine.match(message, tenant_id)
                match_time = calculate_processing_time(start_time)

                debug_info["test_match"] = {
                    "message": message[:50],
                    "tenant_id": tenant_id,
                    "match_found": rule_match is not None,
                    "match_time_ms": match_time,
                }

                if rule_match:
                    debug_info["test_match"]["match_details"] = {
                        "intent": rule_match.intent,
                        "confidence": rule_match.confidence,
                        "rule_id": rule_match.rule_id,
                        "entities": rule_match.entities,
                    }

            except Exception as e:
                debug_info["test_match"] = {
                    "error": str(e),
                    "message": message[:50],
                }

        return debug_info
