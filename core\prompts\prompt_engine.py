import logging
from typing import Dict, Optional, Union
from .template_store import TemplateStore
from .sync_database_template_store import SyncDatabaseTemplateStore
from .mock_template_store import <PERSON>ckTemplateStore
from .prompt_builder import PromptBuilder

logger = logging.getLogger(__name__)


class PromptEngine:
    def __init__(self, template_store: Union[TemplateStore, SyncDatabaseTemplateStore, MockTemplateStore], prompt_builder: PromptBuilder, llm_client):
        self.template_store = template_store
        self.prompt_builder = prompt_builder
        self.llm_client = llm_client

        # Log which template store is being used
        store_type = type(template_store).__name__
        logger.info(f"PromptEngine initialized with {store_type}")

    @classmethod
    def create_with_database(cls, db_pool=None, prompt_builder=None, llm_client=None):
        """
        Create PromptEngine with database template store.

        Args:
            db_pool: Database connection pool (unused for sync store)
            prompt_builder: PromptBuilder instance
            llm_client: LLM client instance

        Returns:
            PromptEngine instance with database store
        """
        try:
            template_store = SyncDatabaseTemplateStore()
            prompt_builder = prompt_builder or PromptBuilder()
            logger.info("Created PromptEngine with SyncDatabaseTemplateStore")
            return cls(template_store, prompt_builder, llm_client)
        except Exception as e:
            logger.warning(f"Failed to create database template store: {e}")
            raise

    @classmethod
    def create_with_fallback(cls, db_pool=None, prompt_builder=None, llm_client=None):
        """
        Create PromptEngine with database store and mock fallback.

        Args:
            db_pool: Database connection pool (unused for sync store)
            prompt_builder: PromptBuilder instance
            llm_client: LLM client instance

        Returns:
            PromptEngine instance with appropriate store
        """
        prompt_builder = prompt_builder or PromptBuilder()

        # Try synchronous database store first
        try:
            template_store = SyncDatabaseTemplateStore()
            # Test database connectivity
            template_store.list_templates()
            logger.info("Created PromptEngine with SyncDatabaseTemplateStore")
            return cls(template_store, prompt_builder, llm_client)
        except Exception as e:
            logger.warning(f"Database template store unavailable, falling back to mock: {e}")

            # Fallback to mock store
            try:
                template_store = MockTemplateStore()
                logger.info("Created PromptEngine with MockTemplateStore fallback")
                return cls(template_store, prompt_builder, llm_client)
            except Exception as e2:
                logger.error(f"Failed to create mock template store: {e2}")
                raise

    def get(self, template_name: str, inputs: Dict, tenant_id: Optional[str] = None) -> str:
        """
        Get a formatted prompt from a template.

        Args:
            template_name: Name of the template to use
            inputs: Dictionary of inputs to fill template placeholders
            tenant_id: Optional tenant ID for scoped template lookup

        Returns:
            Formatted prompt string
        """
        try:
            # Try to get tenant-specific template if database store supports it
            if isinstance(self.template_store, SyncDatabaseTemplateStore) and tenant_id:
                template = self.template_store.get_template_by_name(template_name, tenant_id)
            else:
                template = self.template_store.get_template_by_name(template_name)

            prompt = self.prompt_builder.build_prompt(template, inputs)
            return self.prompt_builder.format_prompt(prompt)

        except Exception as e:
            logger.error(f"Failed to get template '{template_name}': {e}")
            # Enhanced fallback handling for graceful degradation when templates are missing - FIXED
            logger.warning(f"Using fallback prompt for template '{template_name}' due to: {e}")
            return self._create_fallback_prompt(template_name, inputs)

    def generate_response(self, template_name: str, inputs: Dict, tenant_id: Optional[str] = None) -> Dict:
        """Main interface to generate LLM response."""
        print("[DEBUG] entered into generate_response", template_name)
        try:
            # Get formatted prompt with tenant support
            formatted_prompt = self.get(template_name, inputs, tenant_id)
            response = self.call_llm(formatted_prompt)
            return response
        except Exception as e:
            logger.error(f"Failed to generate response for template '{template_name}': {e}")
            return {"output": f"Error generating response: {str(e)}"}

    def call_llm(self, prompt: str) -> Dict:
        """Send the prompt to the LLM and get response."""
        if not self.llm_client:
            return {"output": "LLM client not available"}

        try:
            result = self.llm_client.send(prompt)
            if not result or 'output' not in result:
                raise RuntimeError("LLM call failed or returned invalid response")
            return result
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            return {"output": f"LLM call failed: {str(e)}"}

    def _create_fallback_prompt(self, template_name: str, inputs: Dict) -> str:
        """
        Create a comprehensive fallback prompt when template loading fails.

        Args:
            template_name: Name of the failed template
            inputs: Input dictionary

        Returns:
            Comprehensive fallback prompt string
        """
        # Comprehensive fallback prompt generation for all template types - FIXED
        message = inputs.get("message", inputs.get("user_query", ""))
        tenant_id = inputs.get("tenant_id", "default")
        context = inputs.get("context", "")
        clarifier_rounds = inputs.get("clarifier_rounds", 0)

        # Intent classification templates
        if "classify" in template_name or "intent" in template_name:
            clarifier_note = ""
            if clarifier_rounds > 0:
                clarifier_note = f"\nNote: This is clarification attempt #{clarifier_rounds}. Provide direct classification."

            return f"""Classify the user message into an intent category.
Tenant: {tenant_id}
Context: {context}
{clarifier_note}
Message: "{message}"

Respond with JSON format:
{{"intent": "intent_name", "confidence": 0.0-1.0, "reasoning": "explanation"}}"""

        # Product search templates
        elif "product" in template_name or "search" in template_name:
            return f"""Help the customer find products based on their request.
Tenant: {tenant_id}
Customer request: "{message}"
Context: {context}

Provide a helpful response about product availability and recommendations."""

        # Customer service templates
        elif "support" in template_name or "service" in template_name:
            return f"""Provide customer support for the following inquiry.
Tenant: {tenant_id}
Customer message: "{message}"
Context: {context}

Respond professionally and helpfully to address their concern."""

        # Order-related templates
        elif "order" in template_name or "tracking" in template_name:
            return f"""Assist with order-related inquiry.
Tenant: {tenant_id}
Customer request: "{message}"
Context: {context}

Provide information about order status, tracking, or related services."""

        # FAQ templates
        elif "faq" in template_name or "help" in template_name:
            return f"""Answer the customer's question based on common FAQ topics.
Tenant: {tenant_id}
Question: "{message}"
Context: {context}

Provide a clear and helpful answer."""

        # Clarification templates
        elif "clarif" in template_name:
            return f"""The customer said: "{message}"
Please ask a clarifying question to better understand their needs.
Tenant: {tenant_id}
Context: {context}"""

        # Generic fallback for unknown template types
        else:
            return f"""Respond to the customer's message appropriately.
Tenant: {tenant_id}
Customer message: "{message}"
Context: {context}

Provide a helpful and professional response."""
