
import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Brain, BarChart3, PenLine, Megaphone } from 'lucide-react';

const AIAgents = () => {
  const agents = [
    {
      icon: <Brain className="w-8 h-8 text-teal-500" />,
      name: "Conversational Sales Assistant",
      role: "Sells like your best rep",
      status: "Live",
      description: "Guides customers to the right products, nudges them to checkout, and applies offers or loyalty — all inside one seamless WhatsApp flow.",
      uplift: "+17% conversion · 1.5× AOV",
      isLive: true
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-teal-500" />,
      name: "Insightful Store Analyst",
      role: "Answers like your store manager",
      status: "Live",
      description: "Your team gets real-time answers to sales, SKUs, or stock — no dashboards, no delays.",
      uplift: "5× faster insights · –30% mismatch",
      isLive: true
    },
    {
      icon: <PenLine className="w-8 h-8 text-teal-500" />,
      name: "Dynamic Content Agent",
      role: "Writes like your fastest marketer",
      status: "Beta",
      description: "Auto-generates SEO product descriptions based on interaction. Fast, clean, on-brand.",
      uplift: "+23% CTR · Faster go-live",
      isLive: false
    },
    {
      icon: <Megaphone className="w-8 h-8 text-teal-500" />,
      name: "Growth Marketing Agent",
      role: "Launches like your growth team",
      status: "Beta",
      description: "Sends personalized campaigns across WhatsApp, Email, and Push — powered by customer behavior and built to drive reorders.",
      uplift: "+40% open rate · +18% repeat orders",
      isLive: false
    }
  ];

  return (
    <section id="agents" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4">
          Briskk's Vertical AI Stack.
          </h2>
          <p className="text-center text-gray-500 max-w-xl mx-auto mt-2 text-sm">
          Four AI agents. One smart store. Built for D2C teams that want to sell smarter.
        </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {agents.map((agent, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full transition-all duration-300 hover:shadow-md hover:scale-[1.02] transition-all border border-gray-100">
                <CardContent className="p-6">
                  <div className="flex items-center justify-center mb-4">
                    <div className="w-16 h-16 bg-teal-50 rounded-full flex items-center justify-center">
                      {agent.icon}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold mb-1 text-gray-800 text-center">
                    {agent.emoji} {agent.name}
                  </h3>
                  
                  <p className="text-sm text-center text-teal-600 font-medium mb-3">
                    {agent.role}
                  </p>
                  
                  <div className="flex justify-center mb-4">
                    <Badge className={`px-3 py-0.5 ${
                      agent.isLive ? 'bg-green-100 text-green-800 border-green-200' : 'bg-amber-100 text-amber-800 border-amber-200'
                    }`}>
                      {agent.isLive ? '✅' : '🧪'} {agent.status}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-700 leading-6 text-center mb-4">
                    {agent.description}
                  </p>
                  
                  <div className="text-center">
                    <Badge variant="outline" className="bg-teal-50 border-teal-200 text-teal-700">
                      {agent.uplift}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AIAgents;
