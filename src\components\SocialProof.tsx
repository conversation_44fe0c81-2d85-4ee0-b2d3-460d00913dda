
import React from 'react';
import { motion } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { MessageSquare } from 'lucide-react';

const SocialProof = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto text-center mb-12"
        >
          <h2 className="text-4xl font-bold mb-4">
            🛍️ Trusted by Founders Building the Future of Retail
          </h2>
          <p className="text-xl text-gray-600">
            Stagbeetle, a fast-growing menswear brand, runs its store operations on Briskk — from POS to Shopify sync — and is now piloting Briskk's AI agent stack to power smarter selling.
          </p>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <Card className="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
            <CardContent className="p-2">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                <div className="flex-shrink-0">
                <Avatar className="w-20 h-20 border-2 border-teal-600 shadow-sm">
                    <AvatarFallback className="bg-teal-100 text-teal-800 text-xl font-semibold">DS</AvatarFallback>
                    {/* User would add an actual image here */}
                    <AvatarImage className= "rounded-lg object-cover" src="/Deepankar_sen_pic.png" alt="Deepankar Sen" />
                  </Avatar>
                </div>
                
                <div className="flex-1 text-center md:text-left">
                  <div className="mb-4 flex flex-col md:flex-row md:items-center gap-2">
                    <h4 className="font-semibold text-xl text-gray-900">Deepankar Sen</h4>
                    <span className="text-gray-500 text-sm">Co-Founder, Stagbeetle</span>
                  </div>
                  
                  <blockquote className="text-gray-700 italic text-lg border-l-4 border-teal-300 pl-4 md:pl-6 py-2">
                          
          “Briskk gave us the operational backbone we needed — inventory visibility, faster checkout, and Shopify sync. 
          We’re now piloting the AI agent layer, and even at this stage, I believe it can add 10% to our monthly revenue without increasing headcount.”
    
                  </blockquote>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="mt-10 text-center"
          >
            <p className="text-lg text-gray-700 mb-4">Want to see how Briskk can work for your brand?</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="bg-teal-600 hover:bg-teal-700 text-white font-medium"
                size="lg"
                onClick={() => document.getElementById('waitlist')?.scrollIntoView({ behavior: 'smooth' })}
              >
                 Book a Live Demo
              </Button>
              <Button 
                variant="outline" 
                className="border-teal-300 text-teal-700 hover:bg-teal-50"
                size="lg"
                onClick={() => document.getElementById('waitlist')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Join Early Access
              </Button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default SocialProof;
