import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, <PERSON><PERSON>, <PERSON>, CreditCard, 
  BarChart, ShieldCheck, Zap, Globe 
} from 'lucide-react';

const Benefits = () => {
  const benefits = [
    {
      icon: <Clock className="h-6 w-6 text-purple-600" />,
      title: "24/7 Availability",
      description: "Always-on customer service and sales support, even outside business hours."
    },
    {
      icon: <Percent className="h-6 w-6 text-purple-600" />,
      title: "Increased Conversion",
      description: "AI-driven product recommendations boost conversion rates by up to 35%."
    },
    {
      icon: <Users className="h-6 w-6 text-purple-600" />,
      title: "Reduced Support Load",
      description: "Automate up to 80% of routine customer service inquiries and requests."
    },
    {
      icon: <CreditCard className="h-6 w-6 text-purple-600" />,
      title: "Higher AOV",
      description: "Strategic upselling and cross-selling capabilities increase average order value."
    },
    {
      icon: <Bar<PERSON>hart className="h-6 w-6 text-purple-600" />,
      title: "Data-Driven Insights",
      description: "Turn customer interactions into actionable business intelligence."
    },
    {
      icon: <ShieldCheck className="h-6 w-6 text-purple-600" />,
      title: "Enterprise Security",
      description: "SOC2 compliant with advanced data protection and privacy controls."
    },
    {
      icon: <Zap className="h-6 w-6 text-purple-600" />,
      title: "Quick Implementation",
      description: "Deploy within days, not months, with our no-code integration platform."
    },
    {
      icon: <Globe className="h-6 w-6 text-purple-600" />,
      title: "Multilingual Support",
      description: "AI agents that communicate fluently in over 30 languages."
    }
  ];

  return (
    <section id="benefits" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            The Briskk advantage for retail
          </h2>
          <p className="text-lg text-gray-600">
            Our specialized retail AI delivers measurable improvements across customer experience, 
            operations efficiency, and revenue growth.
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              className="bg-gray-50 rounded-xl p-6 hover:shadow-md transition-shadow duration-300"
            >
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-5">
                {benefit.icon}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">{benefit.title}</h3>
              <p className="text-gray-600">{benefit.description}</p>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-20 bg-purple-50 rounded-2xl p-8 md:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                ROI you can measure from day one
              </h3>
              <p className="text-gray-600 mb-6">
                Our retail-specific AI solutions deliver clear, measurable value. Typical clients see:
              </p>
              <ul className="space-y-4">
                {[
                  "30-50% reduction in customer service costs",
                  "15-35% increase in conversion rates",
                  "20-40% improvement in customer satisfaction scores",
                  "25-45% reduction in time spent on inventory management"
                ].map((item, i) => (
                  <li key={i} className="flex items-start">
                    <svg className="h-6 w-6 text-green-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="grid grid-cols-2 gap-6">
              {[
                { label: "Average Cost Reduction", value: "42%" },
                { label: "Revenue Increase", value: "28%" },
                { label: "Customer Satisfaction", value: "+38%" },
                { label: "Implementation Time", value: "3-5 days" }
              ].map((stat, i) => (
                <div key={i} className="bg-white rounded-xl p-6 shadow-sm text-center">
                  <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">{stat.value}</div>
                  <div className="text-sm text-gray-500">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;
