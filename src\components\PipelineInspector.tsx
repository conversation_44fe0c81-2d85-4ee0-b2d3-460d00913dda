import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Database, 
  Zap, 
  Target, 
  ChevronRight,
  Eye,
  EyeOff
} from 'lucide-react';
import { DebugInfo } from '@/types/api';
import { getSourceColor } from '@/lib/utils';

interface PipelineInspectorProps {
  debugInfo: DebugInfo;
  isLoading: boolean;
  title?: string;
}

const PipelineInspector: React.FC<PipelineInspectorProps> = ({ 
  debugInfo, 
  isLoading, 
  title = "Pipeline Inspector" 
}) => {
  const [showRawJson, setShowRawJson] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'loading':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderSourceChain = () => {
    if (!debugInfo.source_chain) return null;

    const sources = debugInfo.source_chain.split(' → ');
    
    return (
      <div className="flex items-center gap-2 flex-wrap">
        {sources.map((source, index) => (
          <React.Fragment key={source}>
            <Badge 
              variant="outline" 
              className={getSourceColor(source.trim())}
            >
              {source.trim()}
            </Badge>
            {index < sources.length - 1 && (
              <ChevronRight className="w-3 h-3 text-gray-400" />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const renderMetricCard = (
    icon: React.ReactNode,
    label: string,
    value: string | number | undefined,
    status?: 'success' | 'error' | 'warning'
  ) => {
    const getStatusColor = () => {
      switch (status) {
        case 'success':
          return 'text-green-600 bg-green-50';
        case 'error':
          return 'text-red-600 bg-red-50';
        case 'warning':
          return 'text-yellow-600 bg-yellow-50';
        default:
          return 'text-gray-600 bg-gray-50';
      }
    };

    return (
      <div className={`p-3 rounded-lg border ${getStatusColor()}`}>
        <div className="flex items-center gap-2 mb-1">
          {icon}
          <span className="text-sm font-medium">{label}</span>
        </div>
        <div className="text-lg font-semibold">
          {value !== undefined ? value : '—'}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Status Overview */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">{title}</CardTitle>
            {getStatusIcon(debugInfo.request_status)}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Response Time */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Response Time</span>
            <Badge variant="outline">
              {debugInfo.response_time.toFixed(5)}ms
            </Badge>
          </div>

          {/* Source Chain */}
          {debugInfo.source_chain && (
            <div>
              <span className="text-sm text-gray-600 block mb-2">Source Chain</span>
              {renderSourceChain()}
            </div>
          )}

          {/* Intent & Confidence */}
          {debugInfo.parsed_intent && (
            <div className="grid grid-cols-2 gap-3">
              <div>
                <span className="text-sm text-gray-600 block">Intent</span>
                <Badge className="mt-1">{debugInfo.parsed_intent}</Badge>
              </div>
              {debugInfo.confidence_score !== undefined && (
                <div>
                  <span className="text-sm text-gray-600 block">Confidence</span>
                  <Badge 
                    variant="outline" 
                    className={debugInfo.confidence_score > 0.8 ? 'text-green-600' : 'text-yellow-600'}
                  >
                    {(debugInfo.confidence_score * 100).toFixed(1)}%
                  </Badge>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid grid-cols-2 gap-3">
        {renderMetricCard(
          <Database className="w-4 h-4" />,
          "Cache",
          debugInfo.cache_status === 'hit' ? 'Hit' : 'Miss',
          debugInfo.cache_status === 'hit' ? 'success' : undefined
        )}
        
        {renderMetricCard(
          <Target className="w-4 h-4" />,
          "Tool Status",
          debugInfo.tool_execution_status === 'success' ? 'Success' : 
          debugInfo.tool_execution_status === 'error' ? 'Error' : 'Pending',
          debugInfo.tool_execution_status as any
        )}
        
        {renderMetricCard(
          <Zap className="w-4 h-4" />,
          "Fallback",
          debugInfo.fallback_used ? 'Used' : 'Not Used',
          debugInfo.fallback_used ? 'warning' : 'success'
        )}
        
        {renderMetricCard(
          <Clock className="w-4 h-4" />,
          "Processing",
          debugInfo.processing_latency ? `${debugInfo.processing_latency.toFixed(5)}ms` : undefined
        )}
      </div>

      {/* Error Display */}
      {debugInfo.error_message && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">Error</p>
                <p className="text-sm text-red-700 mt-1">{debugInfo.error_message}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Raw JSON Toggle */}
      {debugInfo.raw_response && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Raw Response</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRawJson(!showRawJson)}
                >
                  {showRawJson ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  {showRawJson ? 'Hide' : 'Show'}
                </Button>
              </div>
            </div>
          </CardHeader>
          {showRawJson && (
            <CardContent>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-64">
                {JSON.stringify(debugInfo.raw_response, null, 2)}
              </pre>
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
};

export default PipelineInspector;
