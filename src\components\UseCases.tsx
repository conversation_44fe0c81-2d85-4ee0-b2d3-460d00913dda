
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, ShoppingCart, BadgePercent, MessageSquare } from 'lucide-react';

const UseCases = () => {
  const cases = [
    {
      icon: <ShoppingCart className="w-10 h-10 text-briskk-600" />,
      title: "Personalized Shopping Assistant",
      description: "AI agents that understand customer preferences and provide tailored product recommendations, increasing conversion rates by 37% on average.",
      stats: [
        { value: "37%", label: "Higher Conversion" },
        { value: "42%", label: "Return Customer Rate" },
        { value: "3.2x", label: "Cart Value Increase" }
      ]
    },
    {
      icon: <BadgePercent className="w-10 h-10 text-briskk-600" />,
      title: "Dynamic Pricing & Promotions",
      description: "Smart AI that optimizes pricing strategies in real-time based on demand, inventory levels, and competitor analysis.",
      stats: [
        { value: "22%", label: "Revenue Growth" },
        { value: "31%", label: "Margin Improvement" },
        { value: "5.3x", label: "Promotion ROI" }
      ]
    },
    {
      icon: <MessageSquare className="w-10 h-10 text-briskk-600" />,
      title: "24/7 Customer Support",
      description: "Context-aware AI support agents that resolve 87% of customer inquiries instantly without human intervention.",
      stats: [
        { value: "87%", label: "First Contact Resolution" },
        { value: "94%", label: "Customer Satisfaction" },
        { value: "68%", label: "Support Cost Reduction" }
      ]
    }
  ];

  return (
    <section id="use-cases" className="py-24 bg-gradient-to-b from-white to-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-4xl font-bold text-slate-900 mb-5">
            Real-world retail transformations
          </h2>
          <p className="text-xl text-slate-600">
            See how our specialized AI agents are revolutionizing the retail experience
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {cases.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="mb-6">
                {item.icon}
              </div>
              
              <h3 className="text-2xl font-bold text-slate-900 mb-4">{item.title}</h3>
              <p className="text-slate-600 mb-8">{item.description}</p>
              
              <div className="grid grid-cols-3 gap-2 mb-6">
                {item.stats.map((stat, i) => (
                  <div key={i} className="text-center">
                    <div className="text-2xl font-bold text-briskk-600">{stat.value}</div>
                    <div className="text-sm text-slate-500">{stat.label}</div>
                  </div>
                ))}
              </div>
              
              <Button variant="outline" className="w-full">
                View Case Study <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default UseCases;
