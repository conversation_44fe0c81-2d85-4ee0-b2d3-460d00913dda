"""
Factory patterns for reducing response building complexity.

This module contains factory classes for:
- Response type creation
- Metadata building
- Error response generation
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from ..intent_config import FALLBACK_INTENT

logger = logging.getLogger(__name__)


class ResponseFactory(ABC):
    """Abstract base class for response factories."""

    @abstractmethod
    def create_response(self, **kwargs) -> Dict[str, Any]:
        """Create response with given parameters."""
        pass

    @abstractmethod
    def get_response_type(self) -> str:
        """Get the response type identifier."""
        pass


class RuleResponseFactory(ResponseFactory):
    """Factory for rule-based responses."""

    def create_response(
        self,
        rule_result=None,
        processing_time=0.0,
        source_chain=None,
        session_id=None,
        tenant_id=None,
        tenant_metadata=None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create rule response."""
        if not rule_result:
            raise ValueError("rule_result is required for rule response")

        return {
            "intent": rule_result.intent,
            "confidence": rule_result.confidence,
            "entities": rule_result.entities,
            "source": "rule",
            "source_chain": " → ".join(source_chain or ["rule"]),
            "processing_time_ms": processing_time,
            "rule_id": rule_result.rule_id,
            "rule_source": rule_result.source,
            "pattern_matched": rule_result.pattern_matched,
            # Rule reasoning debug integration - FIXED
            "reasoning": getattr(rule_result, 'reasoning', ''),
            "session_id": session_id,
            "tenant_id": tenant_id,
            "tenant_metadata": tenant_metadata or {},
            "clarification_needed": False,
        }

    def get_response_type(self) -> str:
        return "rule"


class CacheResponseFactory(ResponseFactory):
    """Factory for cache-based responses."""

    def create_response(
        self,
        cached_result=None,
        processing_time=0.0,
        source_chain=None,
        session_id=None,
        tenant_id=None,
        tenant_metadata=None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create cache response."""
        if not cached_result:
            raise ValueError("cached_result is required for cache response")

        # Build response with cache metadata
        response = {
            "intent": cached_result.get("intent", "Unknown"),
            "confidence": cached_result.get("confidence", 0.0),
            "entities": cached_result.get("entities", {}),
            "source": "cache",
            "source_chain": " → ".join(source_chain or ["cache"]),
            "processing_time_ms": processing_time,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "tenant_metadata": tenant_metadata or {},
            "clarification_needed": False,
        }

        # Preserve original source information
        if "original_source" in cached_result:
            response["original_source"] = cached_result["original_source"]

        # Copy cache-specific metadata
        cache_fields = [
            "rule_id",
            "rule_source",
            "pattern_matched",
            "model_used",
            "reasoning",
            "tokens_used",
        ]
        for field in cache_fields:
            if field in cached_result:
                response[field] = cached_result[field]

        return response

    def get_response_type(self) -> str:
        return "cache"


class LLMResponseFactory(ResponseFactory):
    """Factory for LLM-based responses."""

    def create_response(
        self,
        llm_result=None,
        processing_time=0.0,
        source_chain=None,
        session_id=None,
        tenant_id=None,
        tenant_metadata=None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create LLM response."""
        if not llm_result:
            raise ValueError("llm_result is required for LLM response")

        # Determine source based on confidence
        confidence = llm_result.confidence
        source = "llm" if confidence >= 0.8 else "llm_low_confidence"

        return {
            "intent": llm_result.intent,
            "confidence": confidence,
            "entities": llm_result.entities,
            "source": source,
            "source_chain": " → ".join(source_chain or ["llm"]),
            "processing_time_ms": processing_time,
            "model_used": llm_result.model_used,
            "reasoning": llm_result.reasoning,
            "tokens_used": llm_result.tokens_used,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "tenant_metadata": tenant_metadata or {},
            "clarification_needed": confidence < 0.8,
        }

    def get_response_type(self) -> str:
        return "llm"


class FallbackResponseFactory(ResponseFactory):
    """Factory for fallback responses."""

    def create_response(
        self,
        intent=None,
        source_chain=None,
        session_id=None,
        tenant_id=None,
        tenant_metadata=None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create fallback response."""
        return {
            "intent": intent or FALLBACK_INTENT,
            "confidence": 0.0,
            "entities": {},
            "source": "fallback",
            "source_chain": " → ".join(source_chain or ["fallback"]),
            "processing_time_ms": 0.0,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "tenant_metadata": tenant_metadata or {},
            "clarification_needed": False,
            "error": "No suitable parsing method succeeded",
        }

    def get_response_type(self) -> str:
        return "fallback"


class ErrorResponseFactory(ResponseFactory):
    """Factory for error responses."""

    def create_response(
        self,
        error_message=None,
        error_type="parsing_error",
        source_chain=None,
        session_id=None,
        tenant_id=None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create error response."""
        return {
            "intent": "error",
            "confidence": 0.0,
            "entities": {},
            "source": "error",
            "source_chain": " → ".join(source_chain or ["error"]),
            "processing_time_ms": 0.0,
            "session_id": session_id,
            "tenant_id": tenant_id,
            "error": error_message or "Unknown error occurred",
            "error_type": error_type,
            "clarification_needed": False,
        }

    def get_response_type(self) -> str:
        return "error"


class ResponseFactoryRegistry:
    """Registry for managing response factories."""

    def __init__(self):
        self._factories = {}
        self._register_default_factories()

    def _register_default_factories(self):
        """Register default response factories."""
        self.register("rule", RuleResponseFactory())
        self.register("cache", CacheResponseFactory())
        self.register("llm", LLMResponseFactory())
        self.register("fallback", FallbackResponseFactory())
        self.register("error", ErrorResponseFactory())

    def register(self, response_type: str, factory: ResponseFactory):
        """Register a response factory."""
        self._factories[response_type] = factory

    def get_factory(self, response_type: str) -> ResponseFactory:
        """Get factory for response type."""
        if response_type not in self._factories:
            raise ValueError(f"Unknown response type: {response_type}")
        return self._factories[response_type]

    def create_response(self, response_type: str, **kwargs) -> Dict[str, Any]:
        """Create response using appropriate factory."""
        factory = self.get_factory(response_type)
        return factory.create_response(**kwargs)

    def get_available_types(self) -> List[str]:
        """Get list of available response types."""
        return list(self._factories.keys())


class MetadataFactory:
    """Factory for creating response metadata."""

    @staticmethod
    def create_tenant_metadata(
        vertical: str = "unknown", custom_intents_count: int = 0
    ) -> Dict[str, Any]:
        """Create tenant metadata dictionary."""
        return {
            "vertical": vertical,
            "custom_intents_count": custom_intents_count,
            "has_custom_rules": custom_intents_count > 0,
        }

    @staticmethod
    def create_performance_metadata(
        processing_times: Dict[str, float]
    ) -> Dict[str, Any]:
        """Create performance metadata from timing data."""
        total_time = sum(processing_times.values())

        metadata = {
            "total_processing_time_ms": total_time,
            "component_times": processing_times,
        }

        # Add performance breakdown percentages
        if total_time > 0:
            metadata["time_breakdown"] = {
                component: (time_ms / total_time) * 100
                for component, time_ms in processing_times.items()
            }

        return metadata

    @staticmethod
    def create_debug_metadata(source_chain: List[str], metadata_obj) -> Dict[str, Any]:
        """Create debug metadata for troubleshooting."""
        debug_info = {
            "source_chain": source_chain,
            "steps_attempted": len(source_chain),
        }

        # Add component-specific debug info
        if hasattr(metadata_obj, "rule_matched"):
            debug_info["rule_engine"] = {
                "matched": metadata_obj.rule_matched,
                "time_ms": getattr(metadata_obj, "rule_check_time_ms", 0),
            }

        if hasattr(metadata_obj, "cache_hit"):
            debug_info["cache"] = {
                "hit": metadata_obj.cache_hit,
                "time_ms": getattr(metadata_obj, "cache_check_time_ms", 0),
            }

        if hasattr(metadata_obj, "llm_used"):
            debug_info["llm"] = {
                "used": metadata_obj.llm_used,
                "time_ms": getattr(metadata_obj, "llm_time_ms", 0),
            }

        if hasattr(metadata_obj, "clarifier_triggered"):
            debug_info["clarifier"] = {
                "triggered": metadata_obj.clarifier_triggered,
                "time_ms": getattr(metadata_obj, "clarifier_time_ms", 0),
            }

        return debug_info


# Global registry instance
response_factory_registry = ResponseFactoryRegistry()
