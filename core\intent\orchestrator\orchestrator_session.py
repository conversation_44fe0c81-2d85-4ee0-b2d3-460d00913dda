"""
Session management for intent parsing orchestration.

This module handles session state management, clarifier round tracking,
and session context operations for the orchestrator.
"""

import logging
from typing import Dict, Optional, Any
from core.session.session_state import SessionStore
from clients.redis_client import RedisClient

logger = logging.getLogger(__name__)


class OrchestrationSessionManager:
    """Manages session state and clarifier round tracking for orchestration."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def create_default_session_store(self) -> SessionStore:
        """Create default SessionStore using the same Redis client as clarifier."""
        try:
            # Use the same Redis client as clarifier if available
            if self.orchestrator.clarifier and hasattr(self.orchestrator.clarifier, 'redis_client'):
                redis_client = self.orchestrator.clarifier.redis_client
            else:
                # Create new Redis client with default settings
                import config.settings as settings
                redis_client = RedisClient(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB
                )
            return SessionStore(redis_client)
        except Exception as e:
            logger.warning(f"Failed to create SessionStore: {e}")
            # Return a session store with None redis client - SessionStore should handle this gracefully
            return SessionStore(None)

    def get_session_context(self, tenant_id: Optional[str], user_id: Optional[str]) -> Dict[str, Any]:
        """Get session context for clarifier round tracking."""
        if not user_id or not self.orchestrator.session_store:
            return {}

        try:
            return self.orchestrator.session_store.get_context(tenant_id or "default", user_id)
        except Exception as e:
            logger.warning(f"Failed to get session context: {e}")
            return {}

    def check_clarifier_max_rounds(self, clarifier_rounds: int) -> bool:
        """Check if clarifier has reached maximum rounds (3)."""
        return clarifier_rounds >= 3

    def reset_clarifier_rounds(self, tenant_id: Optional[str], user_id: Optional[str]) -> None:
        """Reset clarifier rounds to 0 in session context."""
        if not user_id or not self.orchestrator.session_store:
            return

        try:
            context = self.orchestrator.session_store.get_context(tenant_id or "default", user_id)
            context["clarifier_rounds"] = 0
            self.orchestrator.session_store.save_context(tenant_id or "default", user_id, context)
        except Exception as e:
            logger.warning(f"Failed to reset clarifier rounds: {e}")

    def increment_clarifier_rounds(self, tenant_id: Optional[str], user_id: Optional[str]) -> None:
        """Increment clarifier rounds in session context."""
        if not user_id or not self.orchestrator.session_store:
            return

        try:
            self.orchestrator.session_store.increment_clarifier_round(tenant_id or "default", user_id)
        except Exception as e:
            logger.warning(f"Failed to increment clarifier rounds: {e}")

    def validate_session_state(self, tenant_id: Optional[str], user_id: Optional[str]) -> Dict[str, Any]:
        """
        Validate session state for debugging and health checks.
        
        Returns:
            Dictionary with session validation results
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "session_info": {}
        }

        if not self.orchestrator.session_store:
            validation_result["warnings"].append("Session store not initialized")
            return validation_result

        if not user_id:
            validation_result["warnings"].append("No user_id provided for session validation")
            return validation_result

        try:
            context = self.get_session_context(tenant_id, user_id)
            clarifier_rounds = context.get("clarifier_rounds", 0)
            
            validation_result["session_info"] = {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "clarifier_rounds": clarifier_rounds,
                "max_rounds_reached": self.check_clarifier_max_rounds(clarifier_rounds),
                "context_keys": list(context.keys()) if context else []
            }

            # Check for potential issues
            if clarifier_rounds >= 3:
                validation_result["warnings"].append(f"Clarifier rounds at maximum ({clarifier_rounds})")
            
            if not context:
                validation_result["warnings"].append("Empty session context")

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Session validation failed: {e}")

        return validation_result

    def cleanup_session(self, tenant_id: Optional[str], user_id: Optional[str]) -> bool:
        """
        Clean up session data for a specific user.
        
        Returns:
            True if cleanup successful, False otherwise
        """
        if not user_id or not self.orchestrator.session_store:
            return False

        try:
            # Reset clarifier rounds
            self.reset_clarifier_rounds(tenant_id, user_id)
            
            # Clear session context if needed
            context = self.get_session_context(tenant_id, user_id)
            if context:
                # Keep essential context, clear temporary data
                cleaned_context = {
                    key: value for key, value in context.items()
                    if key in ["user_preferences", "tenant_settings"]
                }
                self.orchestrator.session_store.save_context(
                    tenant_id or "default", user_id, cleaned_context
                )
            
            logger.info(f"Session cleanup completed for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Session cleanup failed for user {user_id}: {e}")
            return False

    def get_session_debug_info(self, tenant_id: Optional[str], user_id: Optional[str]) -> Dict[str, Any]:
        """
        Get detailed session information for debugging.
        
        Returns:
            Dictionary with session debug information
        """
        debug_info = {
            "session_store_available": self.orchestrator.session_store is not None,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "context": {},
            "validation": {}
        }

        if user_id and self.orchestrator.session_store:
            try:
                debug_info["context"] = self.get_session_context(tenant_id, user_id)
                debug_info["validation"] = self.validate_session_state(tenant_id, user_id)
            except Exception as e:
                debug_info["error"] = str(e)

        return debug_info
