"""
Fallback handling package for intent parsing orchestration.

This package contains all fallback, escalation, and error recovery
logic for the intent parsing system.
"""

from .fallback_handler import FallbackHandler
from .escalation_handler import EscalationHandler
from .recovery_strategies import ErrorRecoveryStrategy

__all__ = [
    "FallbackHandler",
    "EscalationHandler", 
    "ErrorRecoveryStrategy",
]
