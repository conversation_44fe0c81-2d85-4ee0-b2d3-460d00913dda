"""
Legacy error tracking interface for backward compatibility.

This module provides a legacy interface that delegates to the new
modular error tracking system.
"""

import logging
from typing import Dict, List, Optional, Any

from .error import (
    ErrorEvent, ErrorCategory, ErrorSeverity,
    get_error_collector, get_error_analyzer,
    track_intent_error
)

logger = logging.getLogger(__name__)


class ErrorTracker:
    """
    Legacy error tracker that delegates to the new modular system.

    This class maintains backward compatibility while using the new
    error tracking implementation.
    """

    def __init__(self, max_events: int = 1000):
        """Initialize error tracker."""
        self._collector = get_error_collector()
        self._analyzer = get_error_analyzer()

    def track_error(
        self,
        category: ErrorCategory,
        severity: ErrorSeverity,
        component: str,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        tenant_id: Optional[str] = None,
        session_id: Optional[str] = None,
        user_message: Optional[str] = None,
        recovery_attempted: bool = False,
        recovery_successful: bool = False,
    ) -> str:
        """Track an error event."""
        return track_intent_error(
            category, severity, component, error,
            context=context, tenant_id=tenant_id, session_id=session_id,
            user_message=user_message, recovery_attempted=recovery_attempted,
            recovery_successful=recovery_successful
        )

    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period."""
        return self._analyzer.get_error_summary(hours)

    def get_error_details(
        self,
        category: Optional[ErrorCategory] = None,
        component: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get detailed error information with optional filtering."""
        # Get events from collector
        if category:
            events = self._collector.get_errors_by_category(category)
        elif component:
            events = self._collector.get_errors_by_component(component)
        else:
            events = list(self._collector.error_events)

        # Sort and limit
        events.sort(key=lambda x: x.timestamp, reverse=True)
        events = events[:limit]

        return [event.to_dict() for event in events]

    def clear_old_errors(self, hours: int = 168) -> int:
        """Clear errors older than specified hours."""
        return self._collector.clear_old_errors(hours)

    def export_errors(self, format: str = "json") -> str:
        """Export error data in specified format."""
        return self._collector.export_errors(format, limit=1000)


# Global error tracker instance
_error_tracker = ErrorTracker()


def get_error_tracker() -> ErrorTracker:
    """Get the global error tracker instance."""
    return _error_tracker
