import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { TestSession, ChatMessage } from '@/types/api';
import { createTestSession, addMessageToSession, resetSession } from '@/utils/sessionUtils';

interface TestSessionContextType {
  // Intent Parser Session
  intentSession: TestSession;
  addIntentMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateIntentMessageFeedback: (messageId: string, feedback: { helpful: boolean | null; comment?: string }) => void;
  resetIntentSession: () => void;

  // Sales Agent Session
  salesSession: TestSession;
  addSalesMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateSalesMessageFeedback: (messageId: string, feedback: { helpful: boolean | null; comment?: string }) => void;
  resetSalesSession: () => void;

  // Global actions
  resetAllSessions: () => void;
}

const TestSessionContext = createContext<TestSessionContextType | undefined>(undefined);

interface TestSessionProviderProps {
  children: ReactNode;
}

export const TestSessionProvider: React.FC<TestSessionProviderProps> = ({ children }) => {
  // Initialize sessions
  const [intentSession, setIntentSession] = useState<TestSession>(() => createTestSession());
  const [salesSession, setSalesSession] = useState<TestSession>(() => createTestSession());

  // Intent Parser session methods
  const addIntentMessage = useCallback((message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    setIntentSession(prevSession => addMessageToSession(prevSession, message));
  }, []);

  const updateIntentMessageFeedback = useCallback((messageId: string, feedback: { helpful: boolean | null; comment?: string }) => {
    setIntentSession(prevSession => ({
      ...prevSession,
      messages: prevSession.messages.map(msg =>
        msg.id === messageId ? { ...msg, feedback } : msg
      )
    }));
  }, []);

  const resetIntentSession = useCallback(() => {
    setIntentSession(prevSession => resetSession(prevSession));
  }, []);

  // Sales Agent session methods
  const addSalesMessage = useCallback((message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    setSalesSession(prevSession => addMessageToSession(prevSession, message));
  }, []);

  const updateSalesMessageFeedback = useCallback((messageId: string, feedback: { helpful: boolean | null; comment?: string }) => {
    setSalesSession(prevSession => ({
      ...prevSession,
      messages: prevSession.messages.map(msg =>
        msg.id === messageId ? { ...msg, feedback } : msg
      )
    }));
  }, []);

  const resetSalesSession = useCallback(() => {
    setSalesSession(prevSession => resetSession(prevSession));
  }, []);

  // Global reset
  const resetAllSessions = useCallback(() => {
    resetIntentSession();
    resetSalesSession();
  }, [resetIntentSession, resetSalesSession]);

  const value: TestSessionContextType = {
    intentSession,
    addIntentMessage,
    updateIntentMessageFeedback,
    resetIntentSession,
    salesSession,
    addSalesMessage,
    updateSalesMessageFeedback,
    resetSalesSession,
    resetAllSessions,
  };

  return (
    <TestSessionContext.Provider value={value}>
      {children}
    </TestSessionContext.Provider>
  );
};

export const useTestSession = (): TestSessionContextType => {
  const context = useContext(TestSessionContext);
  if (context === undefined) {
    throw new Error('useTestSession must be used within a TestSessionProvider');
  }
  return context;
};
