import { useState, useCallback } from 'react';
import { intentParserApi } from '@/api/client';
import { IntentParseRequest, IntentParseResponse, DebugInfo, ApiError } from '@/types/api';

interface UseIntentParserReturn {
  parseIntent: (request: IntentParseRequest) => Promise<IntentParseResponse | null>;
  isLoading: boolean;
  error: string | null;
  debugInfo: DebugInfo;
  clearError: () => void;
}

// Exponential backoff retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 8000,  // 8 seconds
};

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const calculateDelay = (attempt: number): number => {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(2, attempt);
  return Math.min(delay, RETRY_CONFIG.maxDelay);
};

export const useIntentParser = (): UseIntentParserReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    request_status: 'idle',
    response_time: 0,
  });

  const parseIntent = useCallback(async (request: IntentParseRequest): Promise<IntentParseResponse | null> => {
    setIsLoading(true);
    setError(null);
    
    const startTime = Date.now();
    
    setDebugInfo({
      request_status: 'loading',
      response_time: 0,
    });

    let lastError: ApiError | null = null;

    // Retry logic with exponential backoff
    for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {
      try {
        const response = await intentParserApi.parseIntent(request);
        const responseTime = Date.now() - startTime;

        // Update debug info with successful response
        setDebugInfo({
          request_status: 'success',
          response_time: responseTime,
          parsed_intent: response.intent,
          confidence_score: response.confidence,
          source_chain: response.source_chain,
          tool_execution_status: 'success',
          cache_status: response.processing_metadata?.cache_hit ? 'hit' : 'miss',
          fallback_used: response.source === 'llm' && response.confidence < 0.8,
          processing_latency: response.processing_time_ms,
          raw_response: response,
        });

        setIsLoading(false);
        return response;

      } catch (err) {
        lastError = err as ApiError;
        
        // If this is the last attempt, don't wait
        if (attempt === RETRY_CONFIG.maxRetries) {
          break;
        }

        // Wait before retrying (exponential backoff)
        const delay = calculateDelay(attempt);
        await sleep(delay);
      }
    }

    // All retries failed
    const responseTime = Date.now() - startTime;
    const errorMessage = lastError?.message || 'Failed to parse intent after multiple attempts';
    
    setError(errorMessage);
    setDebugInfo({
      request_status: 'error',
      response_time: responseTime,
      error_message: errorMessage,
      tool_execution_status: 'error',
    });
    
    setIsLoading(false);
    return null;
  }, []);

  const clearError = useCallback(() => {
    setError(null);
    setDebugInfo(prev => ({
      ...prev,
      request_status: 'idle',
      error_message: undefined,
    }));
  }, []);

  return {
    parseIntent,
    isLoading,
    error,
    debugInfo,
    clearError,
  };
};
