"""
LLM step implementation for intent parsing orchestration.

This module contains the LLM parsing step logic that handles
Azure OpenAI-based intent detection.
"""

import time
import logging
from typing import Dict, Optional, Any, List

from ...intent_utils import (
    log_parsing_result,
    log_llm_event,
    log_general_event,
    should_trigger_clarifier,
    is_high_confidence,
    calculate_processing_time,
)
from ...response import ParseResponseBuilder
from ...intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class LLMStep:
    """Handles LLM parsing step in the orchestration flow."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def execute(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
        session_context: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Execute LLM parsing step.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking
            user_id: User identifier for session state tracking
            session_context: Session context for conversation history

        Returns:
            LLM parsing result or None if LLM disabled/failed
        """
        if not self.orchestrator.enable_llm or not self.orchestrator.llm_parser:
            log_general_event("llm_disabled")
            return None

        llm_start = time.perf_counter()
        source_chain.append("llm")
        metadata.add_source("llm")

        try:
            # Add original message and session context for LLM
            context = {
                "original_message": message,
                "locale": locale,
                "session_context": session_context or {}
            }

            llm_result = self.orchestrator.llm_parser.parse_intent(
                message, tenant_id, context
            )

            metadata.llm_call_time_ms = calculate_processing_time(llm_start)
            metadata.llm_called = True

            if llm_result:
                return self._handle_llm_success(
                    llm_result, message, tenant_id, metadata,
                    response_builder, source_chain
                )
            else:
                log_llm_event("failed")
                return None

        except Exception as e:
            return self._handle_llm_error(e, llm_start, metadata)

    def _handle_llm_success(
        self,
        llm_result,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Dict[str, Any]:
        """Handle successful LLM parsing."""
        # Performance metrics enhancement integration - FIXED
        metadata.tokens_used = getattr(llm_result, 'tokens_used', 0)
        metadata.model_used = getattr(llm_result, 'model_used', None)
        metadata.intent_confidence = llm_result.confidence

        # Check if clarifier should be triggered
        clarification_needed = should_trigger_clarifier(llm_result.confidence)

        # Build response
        result = response_builder.build_llm_response(
            llm_result,
            metadata.llm_call_time_ms,
            source_chain,
            "llm",
            clarification_needed,
        )

        # Cache successful LLM results (only if high confidence)
        if is_high_confidence(llm_result.confidence):
            self.orchestrator.query_cache.set(message, result, tenant_id)

        log_parsing_result(
            "llm_success", llm_result.intent, llm_result.confidence
        )
        log_llm_event("success", model=llm_result.model_used)
        return result

    def _handle_llm_error(
        self,
        error: Exception,
        llm_start: float,
        metadata: ParseMetadata,
    ) -> None:
        """Handle LLM parsing errors."""
        metadata.add_error(f"LLM parsing failed: {str(error)}")
        metadata.llm_call_time_ms = calculate_processing_time(llm_start)
        log_llm_event("error", error=str(error))
        logger.error(f"LLM step failed: {error}")
        return None

    def validate_llm(self) -> Dict[str, Any]:
        """
        Validate LLM state.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": {}
        }

        try:
            # Check if LLM is enabled and available
            if not self.orchestrator.enable_llm:
                validation["info"]["llm_enabled"] = False
                return validation

            if not self.orchestrator.llm_parser:
                validation["errors"].append("LLM enabled but parser not initialized")
                validation["valid"] = False
                return validation

            validation["info"]["llm_enabled"] = True

            # Validate LLM configuration
            llm_validation = self.orchestrator.llm_parser.validate_configuration()
            validation["info"]["configuration"] = llm_validation

            if not llm_validation["valid"]:
                validation["errors"].extend(llm_validation["errors"])
                validation["valid"] = False

            # Test model availability if validation passes
            if llm_validation["valid"]:
                try:
                    model_tests = self.orchestrator.llm_parser.test_models()
                    validation["info"]["model_tests"] = model_tests
                    
                    if not model_tests["primary_model"]["available"]:
                        validation["warnings"].append("Primary model not available")
                    
                    if not model_tests["fallback_model"]["available"]:
                        validation["warnings"].append("Fallback model not available")

                except Exception as e:
                    validation["warnings"].append(f"Model availability test failed: {e}")

        except Exception as e:
            validation["errors"].append(f"LLM validation failed: {e}")
            validation["valid"] = False

        return validation

    def get_debug_info(self, message: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get debug information for LLM step.
        
        Args:
            message: Test message
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with debug information
        """
        debug_info = {
            "step": "llm",
            "llm_enabled": self.orchestrator.enable_llm,
            "llm_available": self.orchestrator.llm_parser is not None,
            "validation": self.validate_llm(),
        }

        if (self.orchestrator.enable_llm and 
            self.orchestrator.llm_parser and 
            message):
            try:
                # Test LLM parsing
                start_time = time.perf_counter()
                context = {
                    "original_message": message,
                    "locale": "en",
                    "session_context": {}
                }
                llm_result = self.orchestrator.llm_parser.parse_intent(
                    message, tenant_id, context
                )
                parse_time = calculate_processing_time(start_time)

                debug_info["test_parse"] = {
                    "message": message[:50],
                    "tenant_id": tenant_id,
                    "parse_successful": llm_result is not None,
                    "parse_time_ms": parse_time,
                }

                if llm_result:
                    debug_info["test_parse"]["result"] = {
                        "intent": llm_result.intent,
                        "confidence": llm_result.confidence,
                        "model_used": llm_result.model_used,
                        "tokens_used": llm_result.tokens_used,
                        "clarification_needed": should_trigger_clarifier(llm_result.confidence),
                    }

            except Exception as e:
                debug_info["test_parse"] = {
                    "error": str(e),
                    "message": message[:50],
                }

        return debug_info
