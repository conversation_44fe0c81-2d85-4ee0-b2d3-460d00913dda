"""
Performance tracking for intent parsing operations.

This module provides comprehensive performance monitoring and analysis
capabilities for the intent parsing system.
"""

import time
import logging
from typing import Dict, List, Optional, Any
from collections import deque
from .intent_config import PERFORMANCE_STATS_TEMPLATE

logger = logging.getLogger(__name__)


def get_performance_stats_template() -> Dict[str, Any]:
    """Get a fresh copy of the performance stats template."""
    return PERFORMANCE_STATS_TEMPLATE.copy()


class PerformanceTracker:
    """
    Tracks and analyzes performance metrics for intent parsing operations.
    
    This class provides detailed performance monitoring including:
    - Request counts and timing statistics
    - Source distribution analysis
    - Tenant-specific performance metrics
    - Detailed timing breakdowns
    """

    def __init__(self, max_detailed_timings: int = 1000):
        """
        Initialize performance tracker.
        
        Args:
            max_detailed_timings: Maximum number of detailed timing records to keep
        """
        self.stats = get_performance_stats_template()
        self.detailed_timings: deque = deque(maxlen=max_detailed_timings)
        self.max_detailed_timings = max_detailed_timings

    def record_request(
        self,
        processing_time_ms: float,
        final_source: str,
        tenant_id: Optional[str] = None,
        tenant_vertical: Optional[str] = None,
        custom_intents_count: int = 0,
        rule_time_ms: float = 0.0,
        cache_time_ms: float = 0.0,
        llm_time_ms: float = 0.0,
        clarifier_time_ms: float = 0.0,
        cache_hit: bool = False,
        rule_matched: bool = False,
        llm_called: bool = False,
        clarifier_triggered: bool = False,
        source_chain: Optional[List[str]] = None,
    ) -> None:
        """
        Record a completed request with detailed metrics.
        
        Args:
            processing_time_ms: Total processing time in milliseconds
            final_source: Final source that provided the result
            tenant_id: Tenant identifier
            tenant_vertical: Tenant business vertical
            custom_intents_count: Number of custom intents for tenant
            rule_time_ms: Time spent in rule engine
            cache_time_ms: Time spent in cache operations
            llm_time_ms: Time spent in LLM calls
            clarifier_time_ms: Time spent in clarifier
            cache_hit: Whether cache was hit
            rule_matched: Whether rule was matched
            llm_called: Whether LLM was called
            clarifier_triggered: Whether clarifier was triggered
            source_chain: Chain of sources attempted
        """
        # Update basic stats
        self.stats["total_requests"] += 1
        self.stats["total_time_ms"] += processing_time_ms

        # Update source-specific stats
        if rule_matched:
            self.stats["rule_matches"] += 1
        
        if cache_hit:
            self.stats["cache_hits"] += 1
        else:
            self.stats["cache_misses"] += 1
        
        if llm_called:
            self.stats["llm_requests"] += 1
        
        if clarifier_triggered:
            self.stats["clarifier_requests"] += 1

        # Record detailed timing
        detailed_timing = {
            "timestamp": time.time(),
            "processing_time_ms": processing_time_ms,
            "final_source": final_source,
            "tenant_id": tenant_id,
            "tenant_vertical": tenant_vertical,
            "custom_intents_count": custom_intents_count,
            "rule_time_ms": rule_time_ms,
            "cache_time_ms": cache_time_ms,
            "llm_time_ms": llm_time_ms,
            "clarifier_time_ms": clarifier_time_ms,
            "cache_hit": cache_hit,
            "rule_matched": rule_matched,
            "llm_called": llm_called,
            "clarifier_triggered": clarifier_triggered,
            "source_chain": source_chain or [],
        }
        
        self.detailed_timings.append(detailed_timing)

    def get_stats(self) -> Dict[str, Any]:
        """
        Get basic performance statistics.
        
        Returns:
            Dictionary with basic performance metrics
        """
        stats = self.stats.copy()
        
        # Calculate derived metrics
        if stats["total_requests"] > 0:
            stats["average_time_ms"] = stats["total_time_ms"] / stats["total_requests"]
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["total_requests"]
            stats["rule_match_rate"] = stats["rule_matches"] / stats["total_requests"]
            stats["llm_usage_rate"] = stats["llm_requests"] / stats["total_requests"]
            stats["clarifier_usage_rate"] = stats["clarifier_requests"] / stats["total_requests"]
        else:
            stats["average_time_ms"] = 0.0
            stats["cache_hit_rate"] = 0.0
            stats["rule_match_rate"] = 0.0
            stats["llm_usage_rate"] = 0.0
            stats["clarifier_usage_rate"] = 0.0
        
        return stats

    def get_detailed_stats(self) -> Dict[str, Any]:
        """
        Get detailed performance statistics including timing breakdowns.
        
        Returns:
            Dictionary with detailed performance statistics
        """
        basic_stats = self.get_stats()

        if not self.detailed_timings:
            return basic_stats

        # Calculate timing breakdowns
        total_rule_time = sum(t["rule_time_ms"] for t in self.detailed_timings)
        total_cache_time = sum(t["cache_time_ms"] for t in self.detailed_timings)
        total_llm_time = sum(t["llm_time_ms"] for t in self.detailed_timings)
        total_clarifier_time = sum(
            t["clarifier_time_ms"] for t in self.detailed_timings
        )

        request_count = len(self.detailed_timings)

        detailed_stats = {
            **basic_stats,
            "timing_breakdown": {
                "average_rule_time_ms": total_rule_time / request_count,
                "average_cache_time_ms": total_cache_time / request_count,
                "average_llm_time_ms": total_llm_time / request_count,
                "average_clarifier_time_ms": total_clarifier_time / request_count,
            },
            "source_distribution": self._calculate_source_distribution(),
            "tenant_distribution": self._calculate_tenant_distribution(),
            "performance_trends": self._calculate_performance_trends(),
        }

        return detailed_stats

    def _calculate_source_distribution(self) -> Dict[str, int]:
        """Calculate distribution of final sources."""
        distribution = {}
        for timing in self.detailed_timings:
            source = timing["final_source"]
            distribution[source] = distribution.get(source, 0) + 1
        return distribution

    def _calculate_tenant_distribution(self) -> Dict[str, Dict[str, Any]]:
        """Calculate performance distribution by tenant."""
        tenant_stats = {}

        for timing in self.detailed_timings:
            tenant_id = timing["tenant_id"] or "default"

            if tenant_id not in tenant_stats:
                tenant_stats[tenant_id] = {
                    "request_count": 0,
                    "total_time_ms": 0.0,
                    "vertical": timing["tenant_vertical"],
                    "custom_intents_count": timing["custom_intents_count"],
                }

            tenant_stats[tenant_id]["request_count"] += 1
            tenant_stats[tenant_id]["total_time_ms"] += timing["processing_time_ms"]

        # Calculate averages
        for tenant_id, stats in tenant_stats.items():
            if stats["request_count"] > 0:
                stats["average_time_ms"] = (
                    stats["total_time_ms"] / stats["request_count"]
                )
            else:
                stats["average_time_ms"] = 0.0

        return tenant_stats

    def _calculate_performance_trends(self) -> Dict[str, Any]:
        """Calculate performance trends over time."""
        if len(self.detailed_timings) < 10:
            return {"insufficient_data": True}

        # Get recent timings (last 100 requests)
        recent_timings = list(self.detailed_timings)[-100:]
        
        # Calculate trend metrics
        recent_avg_time = sum(t["processing_time_ms"] for t in recent_timings) / len(recent_timings)
        
        # Compare with overall average
        overall_avg_time = self.stats["total_time_ms"] / self.stats["total_requests"] if self.stats["total_requests"] > 0 else 0
        
        trend_direction = "stable"
        if recent_avg_time > overall_avg_time * 1.1:
            trend_direction = "degrading"
        elif recent_avg_time < overall_avg_time * 0.9:
            trend_direction = "improving"

        return {
            "recent_average_time_ms": recent_avg_time,
            "overall_average_time_ms": overall_avg_time,
            "trend_direction": trend_direction,
            "sample_size": len(recent_timings)
        }

    def get_performance_percentiles(self) -> Dict[str, float]:
        """Calculate performance percentiles."""
        if not self.detailed_timings:
            return {}

        times = sorted([t["processing_time_ms"] for t in self.detailed_timings])
        
        def percentile(data, p):
            k = (len(data) - 1) * p / 100
            f = int(k)
            c = k - f
            if f == len(data) - 1:
                return data[f]
            return data[f] * (1 - c) + data[f + 1] * c

        return {
            "p50": percentile(times, 50),
            "p75": percentile(times, 75),
            "p90": percentile(times, 90),
            "p95": percentile(times, 95),
            "p99": percentile(times, 99),
        }

    def clear_stats(self) -> None:
        """Clear all performance statistics."""
        self.stats = get_performance_stats_template()
        self.detailed_timings.clear()

    def export_detailed_timings(
        self, limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Export detailed timing data for analysis.
        
        Args:
            limit: Maximum number of timings to export (None for all)
            
        Returns:
            List of detailed timing dictionaries
        """
        if limit is None:
            return list(self.detailed_timings)
        else:
            return list(self.detailed_timings)[-limit:]

    def get_slow_requests(self, threshold_ms: float = 1000) -> List[Dict[str, Any]]:
        """
        Get requests that exceeded the specified time threshold.
        
        Args:
            threshold_ms: Time threshold in milliseconds
            
        Returns:
            List of slow request details
        """
        return [
            timing for timing in self.detailed_timings
            if timing["processing_time_ms"] > threshold_ms
        ]

    def get_component_performance(self) -> Dict[str, Dict[str, float]]:
        """
        Get performance breakdown by component.
        
        Returns:
            Dictionary with component performance metrics
        """
        if not self.detailed_timings:
            return {}

        components = ["rule", "cache", "llm", "clarifier"]
        component_stats = {}

        for component in components:
            time_key = f"{component}_time_ms"
            times = [t[time_key] for t in self.detailed_timings if t[time_key] > 0]
            
            if times:
                component_stats[component] = {
                    "count": len(times),
                    "total_time_ms": sum(times),
                    "average_time_ms": sum(times) / len(times),
                    "min_time_ms": min(times),
                    "max_time_ms": max(times),
                }
            else:
                component_stats[component] = {
                    "count": 0,
                    "total_time_ms": 0.0,
                    "average_time_ms": 0.0,
                    "min_time_ms": 0.0,
                    "max_time_ms": 0.0,
                }

        return component_stats


# Global performance tracker instance
_performance_tracker = PerformanceTracker()


def get_performance_tracker() -> PerformanceTracker:
    """Get the global performance tracker instance."""
    return _performance_tracker
