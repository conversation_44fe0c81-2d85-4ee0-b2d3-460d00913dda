"""
Error collection and storage for intent parsing.

This module handles the collection, storage, and basic management
of error events in the intent parsing system.
"""

import time
import logging
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque

from .error_types import ErrorEvent, Error<PERSON>ategory, ErrorSeverity

logger = logging.getLogger(__name__)


class ErrorCollector:
    """Collects and stores error events for analysis."""

    def __init__(self, max_events: int = 1000):
        """
        Initialize error collector.
        
        Args:
            max_events: Maximum number of error events to keep in memory
        """
        self.max_events = max_events
        self.error_events: deque = deque(maxlen=max_events)
        self.error_counts = defaultdict(int)
        self.category_counts = defaultdict(int)
        self.severity_counts = defaultdict(int)
        self.component_counts = defaultdict(int)
        
        # Pattern tracking for recurring errors
        self.error_patterns = defaultdict(list)

    def collect_error(self, error_event: ErrorEvent) -> str:
        """
        Collect an error event.

        Args:
            error_event: The error event to collect

        Returns:
            Error event ID for reference
        """
        # 🔍 INTENTIONAL ERROR FOR TESTING ERROR SYSTEM SELF-TRACKING
        # This demonstrates how the error tracking system tracks its own errors
        if hasattr(error_event, 'message') and "test_error_system_error" in str(error_event.message):
            raise RuntimeError(f"INTENTIONAL ERROR SYSTEM ERROR in error_collector.py:collect_error:line_45 - Testing error tracking system's ability to track its own errors (300 lines)")

        # Add to events
        self.error_events.append(error_event)

        # Update counters
        error_key = f"{error_event.category.value}:{error_event.error_type}"
        self.error_counts[error_key] += 1
        self.category_counts[error_event.category.value] += 1
        self.severity_counts[error_event.severity.value] += 1
        self.component_counts[error_event.component] += 1

        # Track patterns
        pattern_key = f"{error_event.component}:{error_event.error_type}"
        self.error_patterns[pattern_key].append(error_event.timestamp)

        # Log the error
        self._log_error(error_event)

        # Generate event ID
        event_id = f"{int(error_event.timestamp)}_{error_event.category.value}_{error_event.component}"
        return event_id

    def _log_error(self, event: ErrorEvent) -> None:
        """Log error event with appropriate level."""
        log_message = event.get_short_description()
        
        if event.tenant_id:
            log_message += f" (tenant: {event.tenant_id})"
        
        if event.session_id:
            log_message += f" (session: {event.session_id})"
        
        log_level = event.get_log_level()
        if log_level == "critical":
            logger.critical(log_message)
        elif log_level == "error":
            logger.error(log_message)
        elif log_level == "warning":
            logger.warning(log_message)
        else:
            logger.info(log_message)

    def get_recent_errors(self, hours: int = 24) -> List[ErrorEvent]:
        """
        Get errors from the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of recent error events
        """
        cutoff_time = time.time() - (hours * 3600)
        return [
            event for event in self.error_events 
            if event.timestamp >= cutoff_time
        ]

    def get_errors_by_category(self, category: ErrorCategory) -> List[ErrorEvent]:
        """
        Get errors by category.
        
        Args:
            category: Error category to filter by
            
        Returns:
            List of error events in the category
        """
        return [
            event for event in self.error_events 
            if event.category == category
        ]

    def get_errors_by_component(self, component: str) -> List[ErrorEvent]:
        """
        Get errors by component.
        
        Args:
            component: Component name to filter by
            
        Returns:
            List of error events from the component
        """
        return [
            event for event in self.error_events 
            if event.component == component
        ]

    def get_errors_by_severity(self, severity: ErrorSeverity) -> List[ErrorEvent]:
        """
        Get errors by severity.
        
        Args:
            severity: Error severity to filter by
            
        Returns:
            List of error events with the severity
        """
        return [
            event for event in self.error_events 
            if event.severity == severity
        ]

    def get_error_counts(self) -> Dict[str, int]:
        """Get error counts by type."""
        return dict(self.error_counts)

    def get_category_counts(self) -> Dict[str, int]:
        """Get error counts by category."""
        return dict(self.category_counts)

    def get_severity_counts(self) -> Dict[str, int]:
        """Get error counts by severity."""
        return dict(self.severity_counts)

    def get_component_counts(self) -> Dict[str, int]:
        """Get error counts by component."""
        return dict(self.component_counts)

    def find_patterns(self, hours: int = 24, min_occurrences: int = 3) -> Dict[str, Dict[str, Any]]:
        """
        Find error patterns (recurring errors).
        
        Args:
            hours: Time window to analyze
            min_occurrences: Minimum occurrences to consider a pattern
            
        Returns:
            Dictionary of detected patterns
        """
        import time
        cutoff_time = time.time() - (hours * 3600)
        patterns = {}
        
        for pattern_key, timestamps in self.error_patterns.items():
            recent_timestamps = [
                ts for ts in timestamps 
                if ts >= cutoff_time
            ]
            
            if len(recent_timestamps) >= min_occurrences:
                patterns[pattern_key] = {
                    "count": len(recent_timestamps),
                    "frequency": len(recent_timestamps) / hours,
                    "last_occurrence": max(recent_timestamps),
                    "first_occurrence": min(recent_timestamps),
                }
        
        return patterns

    def clear_old_errors(self, hours: int = 168) -> int:  # Default: 1 week
        """
        Clear errors older than specified hours.
        
        Args:
            hours: Age threshold in hours
            
        Returns:
            Number of errors cleared
        """
        import time
        cutoff_time = time.time() - (hours * 3600)
        initial_count = len(self.error_events)
        
        # Filter out old events
        self.error_events = deque(
            [event for event in self.error_events if event.timestamp >= cutoff_time],
            maxlen=self.max_events
        )
        
        # Rebuild counters
        self._rebuild_counters()
        
        cleared_count = initial_count - len(self.error_events)
        logger.info(f"Cleared {cleared_count} old error events")
        return cleared_count

    def _rebuild_counters(self) -> None:
        """Rebuild all counters from current events."""
        self.error_counts.clear()
        self.category_counts.clear()
        self.severity_counts.clear()
        self.component_counts.clear()
        self.error_patterns.clear()
        
        for event in self.error_events:
            error_key = f"{event.category.value}:{event.error_type}"
            self.error_counts[error_key] += 1
            self.category_counts[event.category.value] += 1
            self.severity_counts[event.severity.value] += 1
            self.component_counts[event.component] += 1
            
            pattern_key = f"{event.component}:{event.error_type}"
            self.error_patterns[pattern_key].append(event.timestamp)

    def get_stats(self) -> Dict[str, Any]:
        """Get basic error statistics."""
        total_errors = len(self.error_events)
        
        if total_errors == 0:
            return {
                "total_errors": 0,
                "categories": {},
                "severities": {},
                "components": {},
                "patterns": {},
            }
        
        return {
            "total_errors": total_errors,
            "categories": self.get_category_counts(),
            "severities": self.get_severity_counts(),
            "components": self.get_component_counts(),
            "patterns": self.find_patterns(),
        }

    def export_errors(self, format: str = "json", limit: Optional[int] = None) -> str:
        """
        Export error data in specified format.
        
        Args:
            format: Export format ("json" or "csv")
            limit: Maximum number of errors to export
            
        Returns:
            Exported data as string
        """
        events_to_export = list(self.error_events)
        if limit:
            events_to_export = events_to_export[-limit:]
        
        if format.lower() == "json":
            import json
            return json.dumps([event.to_dict() for event in events_to_export], indent=2)
        elif format.lower() == "csv":
            import csv
            import io
            
            output = io.StringIO()
            if events_to_export:
                fieldnames = [
                    "timestamp", "category", "severity", "component", 
                    "error_type", "message", "tenant_id", "session_id",
                    "recovery_attempted", "recovery_successful"
                ]
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                
                for event in events_to_export:
                    event_dict = event.to_dict()
                    writer.writerow({
                        key: event_dict.get(key, "") 
                        for key in fieldnames
                    })
            
            return output.getvalue()
        else:
            raise ValueError(f"Unsupported export format: {format}")


# Global error collector instance
_error_collector = ErrorCollector()


def get_error_collector() -> ErrorCollector:
    """Get the global error collector instance."""
    return _error_collector
