import logging
# import asyncio

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from api.routers import sales_agent
from api.routers import embed
from api.routers import intent_parser as intent_router

import config.settings as settings

# from api.routers import rag_router
# from core.rag.db import init_db_pool

# from core.rag.executors import search_products_by_name

from clients.redis_client import RedisClient
from clients.elixir_api import ElixirAP<PERSON>
from core.cache.cache_manager import CacheManager
from core.rag.strategy_router import RAGStrategyRouter
from core.prompts.prompt_engine import PromptEngine
from core.prompts.prompt_builder import PromptBuilder
from core.prompts.template_store import TemplateStore
from core.prompts.llm_adapter import PromptEngineLLMAdapter
from core.fallback.handler import FallbackHandler
from core.intent.intent_detector import IntentParser
from core.intent.intent_detector_adapter import IntentDetectorAdapter
from core.agent.orchestrator import AgentOrchestrator
from core.agent.flow_router import FlowRouter
from api.handlers.sales_agent import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from middlewares.tenant_context import TenantContextMiddleware
from middlewares.trace_logger import TraceLoggerMiddleware
from middlewares.error_handler import ErrorHandlerMiddleware


from core.fallback.static_fallback import StaticFallback
from core.fallback.escalator import Escalator
from core.fallback.clarifier import Clarifier
from core.session.session_state import SessionStore
# from starlette.requests import Request
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment and YAML configs
with open("config/fallback.yml") as f:
    fallback_config = yaml.safe_load(f)
with open("config/cache.yml") as f:
    cache_config = yaml.safe_load(f)

# Initialize Elixir API client
elixir_client = ElixirAPI(settings.ELIXIR_BACKEND_URL)

# Initialize TemplateStore with the Elixir API client
template_store = TemplateStore(elixir_client)

# Initialize PromptBuilder
prompt_builder = PromptBuilder()


llm_client = PromptEngineLLMAdapter()

# Instantiate dependencies
# TODO: Update Redis connection to use @sakshammaurya's cacheManager modular class once merged
# TODO: Centralize Redis configuration settings via redis_client.py instead of hardcoded host/port - Robustness Improvement
redis_client = RedisClient(host="localhost", port=6379, db=0)
session_store = SessionStore(redis_client)
cache_manager = CacheManager(redis_client)
strategy_router = RAGStrategyRouter()
# Initialize fallback components
escalation_api = ElixirAPI()  # Assuming ElixirAPI has methods for escalation
escalator = Escalator(escalation_api)
clarifier = Clarifier(escalator)
static_fallback = StaticFallback()
fallback_handler = FallbackHandler(
    clarifier,
    escalation_api,
    static_fallback,
)
# Use MockTemplateStore for reliable template access during development
from core.prompts.mock_template_store import MockTemplateStore
mock_template_store = MockTemplateStore()
prompt_engine = PromptEngine(
    template_store=mock_template_store, prompt_builder=prompt_builder, llm_client=llm_client
)

# Initialize IntentParser with full functionality enabled
intent_parser = IntentParser(
    config_dir="config", enable_llm=True, enable_clarifier=True
)

# Create adapter for FlowRouter compatibility
intent_detector_adapter = IntentDetectorAdapter(intent_parser)

# Wire together orchestrator and router
orchestrator = AgentOrchestrator(
    cache_manager,
    strategy_router,
    prompt_engine,
    elixir_client,
    fallback_handler,
    session_store,
)
flow_router = FlowRouter(
    intent_detector_adapter, cache_manager, orchestrator, session_store
)

# Initialize FastAPI app
app = FastAPI()

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # All origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Attach handler and intent parser to app state
app.state.sales_agent_handler = SalesAgentHandler(flow_router)
app.state.intent_parser = intent_parser

# Register middleware
app.add_middleware(TenantContextMiddleware, elixir_client=elixir_client)
app.add_middleware(TraceLoggerMiddleware)
app.add_middleware(ErrorHandlerMiddleware)

# Include routers
app.include_router(sales_agent.router)
app.include_router(embed.router)


@app.get("/test-prompt")
def test_prompt(name: str = "Charlie"):
    try:
        inputs = {"name": name}
        response = app.state.sales_agent_handler.flow_router.orchestrator.prompt_engine.generate_response(
            "greeting_template", inputs
        )
        return {"response": response}
    except Exception as e:
        return {"error": str(e)}


app.include_router(intent_router.router)

# app.include_router(rag_router.router, prefix="/api")


@app.get("/ping")
def ping():
    return {"status": "ok"}


if __name__ == "__main__":
    import uvicorn
    import sys

    # Check for --no-reload flag
    reload_enabled = "--no-reload" not in sys.argv

    # Use port 8000 by default, but allow override for testing
    port = 8001 if "--test-port" in sys.argv else 8000

    logger.info(f"Starting Briskk AI Agent FastAPI server on port {port}...")
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=reload_enabled)
