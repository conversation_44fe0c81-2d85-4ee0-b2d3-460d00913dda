"""
Fallback coordination for intent parsing orchestration.

This module coordinates the modular fallback, escalation, and
error recovery components.
"""

import logging
from typing import Dict, Optional, Any, List

from .fallback import Fall<PERSON><PERSON><PERSON><PERSON>, Escalation<PERSON><PERSON><PERSON>, ErrorRecoveryStrategy
from ..intent_metadata import ParseMetadata
from ..response import ParseResponseBuilder

logger = logging.getLogger(__name__)


class FallbackCoordinator:
    """Coordinates all fallback, escalation, and recovery operations."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

        # Initialize handlers
        self.fallback_handler = Fallback<PERSON>andler(orchestrator)
        self.escalation_handler = EscalationHandler(orchestrator)
        self.error_recovery = ErrorRecoveryStrategy(orchestrator)

    def handle_fallback(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Handle fallback when no other method succeeds."""
        return self.fallback_handler.handle_fallback(
            message, tenant_id, session_id, metadata,
            response_builder, source_chain, user_id
        )

    def handle_escalation(
        self,
        message: str,
        clarifier_rounds: int,
        source_chain: List[str],
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Handle escalation scenarios."""
        return self.escalation_handler.handle_escalation(
            message, clarifier_rounds, source_chain, session_id, tenant_id
        )

    def handle_error(
        self,
        error: Exception,
        message: str,
        source_chain: List[str],
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Handle unexpected errors."""
        return self.escalation_handler.handle_error(
            error, message, source_chain, session_id, tenant_id
        )

    def recover_from_rule_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """Attempt recovery from rule engine failure."""
        return self.error_recovery.recover_from_rule_failure(message, tenant_id, error)

    def recover_from_cache_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """Attempt recovery from cache failure."""
        return self.error_recovery.recover_from_cache_failure(message, tenant_id, error)

    def recover_from_llm_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """Attempt recovery from LLM failure."""
        return self.error_recovery.recover_from_llm_failure(message, tenant_id, error)

    def recover_from_clarifier_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """Attempt recovery from clarifier failure."""
        return self.error_recovery.recover_from_clarifier_failure(
            message, tenant_id, session_id, error
        )

    def validate_fallback_system(self) -> Dict[str, Any]:
        """
        Validate the entire fallback system.

        Returns:
            Dictionary with validation results
        """
        return {
            "fallback_handler": self.fallback_handler.validate_fallback_config(),
            "escalation_handler": self.escalation_handler.get_escalation_stats(),
            "error_recovery": self.error_recovery.get_recovery_capabilities(),
        }

    def get_fallback_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive fallback statistics.

        Returns:
            Dictionary with fallback statistics
        """
        return {
            "fallback": self.fallback_handler.get_fallback_stats(),
            "escalation": self.escalation_handler.get_escalation_stats(),
            "recovery": self.error_recovery.get_recovery_capabilities(),
        }


# Legacy classes for backward compatibility
# Note: FallbackHandler is now FallbackCoordinator
# ErrorRecoveryStrategy is imported from fallback package
