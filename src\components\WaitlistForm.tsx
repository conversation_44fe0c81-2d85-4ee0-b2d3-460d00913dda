
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { CalendarClock, MessageSquare, Rocket } from 'lucide-react';

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  brand: z.string().min(2, { message: "Brand name or Shopify URL is required." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  role: z.string().min(1, { message: "Please select your role." })
});

const WaitlistForm = () => {
  const { toast } = useToast();
  const [formStatus, setFormStatus] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      brand: "",
      email: "",
      role: ""
    }
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    setFormStatus("Submitting...");
    
    const formData = new FormData();
    Object.entries(values).forEach(([key, value]) => {
      formData.append(key, value);
    });

    fetch("https://formspree.io/f/xbljebkp", {
      method: "POST",
      body: formData,
      headers: {
        Accept: "application/json",
      },
    })
      .then((response) => {
        if (response.ok) {
          setFormStatus("Thanks for reaching out — we'll be in touch shortly!");
          toast({
            title: "Success!",
            description: "✅ Thanks! We'll be in touch via WhatsApp or email.",
            duration: 5000,
          });
          form.reset();
        } else {
          setFormStatus("Oops! Something went wrong. Please try again.");
          toast({
            title: "Error",
            description: "❌ Something went wrong. Please try again.",
            variant: "destructive",
          });
        }
        setIsSubmitting(false);
      })
      .catch(() => {
        setFormStatus("Oops! Something went wrong. Please try again.");
        toast({
          title: "Error",
          description: "❌ Something went wrong. Please try again.",
          variant: "destructive",
        });
        setIsSubmitting(false);
      });
  }

  return (
    <section id="waitlist" className="py-20 bg-gradient-to-b from-white to-teal-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-xl mx-auto text-center mb-10"
        >
          <h2 className="text-4xl font-bold mb-4">
            💬 Let Briskk Show You What Smart Retail Looks Like
          </h2>
          <p className="text-xl text-gray-600">
            Book a demo to see how Briskk helps your store respond faster, 
            sell smarter, and operate leaner — all through WhatsApp.
          </p>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-md mx-auto"
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Your name" 
                        className="rounded-md px-4 py-3 border border-gray-200 focus:ring-2 focus:ring-[#14b8a6] focus:border-transparent" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="brand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand / Shopify URL</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Your brand or store URL" 
                        className="rounded-md px-4 py-3 border border-gray-200 focus:ring-2 focus:ring-[#14b8a6] focus:border-transparent" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        className="rounded-md px-4 py-3 border border-gray-200 focus:ring-2 focus:ring-[#14b8a6] focus:border-transparent" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="rounded-md px-4 py-3 border border-gray-200 focus:ring-2 focus:ring-[#14b8a6] focus:border-transparent">
                          <SelectValue placeholder="Select your role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="founder">Founder</SelectItem>
                        <SelectItem value="store-ops">Store Ops</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="exploring">Just Exploring</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button 
                type="submit" 
                disabled={isSubmitting} 
                className="w-full bg-[#14b8a6] hover:bg-[#0d9488] hover:scale-105 text-white py-6 text-lg transition-all duration-300 mt-2"
              >
                {isSubmitting ? "Submitting..." : "Book My Demo"}
              </Button>
              
              {formStatus && !isSubmitting && (
                <p className={`text-center text-sm ${
                  formStatus.includes("Thanks") ? "text-green-600" : "text-red-600"
                }`}>
                  {formStatus}
                </p>
              )}
              
              <div className="flex flex-wrap items-center justify-center gap-3 mt-6 text-sm text-gray-500">
                <div className="flex items-center">
                  <CalendarClock className="w-4 h-4 mr-1 text-teal-600" />
                  <span>Takes &lt;30 sec</span>
                </div>
                <div className="flex items-center">
                  <MessageSquare className="w-4 h-4 mr-1 text-teal-600" />
                  <span>1:1 founder walkthrough</span>
                </div>
                <div className="flex items-center">
                  <Rocket className="w-4 h-4 mr-1 text-teal-600" />
                  <span>Join the early pilot</span>
                </div>
              </div>
              
              <p className="text-xs text-center text-gray-400 mt-2">
                Powered by WhatsApp API & Shopify
              </p>
            </form>
          </Form>
        </motion.div>
      </div>
    </section>
  );
};

export default WaitlistForm;
