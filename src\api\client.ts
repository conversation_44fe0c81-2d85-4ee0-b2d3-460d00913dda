import axios, { AxiosInstance, AxiosRequestConfig, AxiosHeaders } from 'axios';
import { 
  IntentParseRequest, 
  IntentParseResponse, 
  SalesAgentRequest, 
  SalesAgentResponse,
  ApiError 
} from '@/types/api';

// Required tenant ID for all requests
// TODO: It will be picked after tenant logs into the debug portal!!
const REQUIRED_TENANT_ID = '0191fc1a-ae52-7796-9d29-5691aba7f284';

// Create axios instance with default configuration
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: '', // Use relative URLs with Vite proxy
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'X-Tenant-ID': REQUIRED_TENANT_ID,
    },
  });

  // Request interceptor to ensure X-Tenant-ID header is always present
  client.interceptors.request.use(
    (config) => {
      // Multiple layers of protection to guarantee the header is never missing
      if (!config.headers) {
        config.headers = new AxiosHeaders();
      }

      // Always set the tenant ID header, regardless of request type
      config.headers['X-Tenant-ID'] = REQUIRED_TENANT_ID;

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for consistent error handling
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      const apiError: ApiError = {
        message: error.response?.data?.error || error.message || 'An unexpected error occurred',
        status: error.response?.status,
        code: error.code,
      };
      return Promise.reject(apiError);
    }
  );

  return client;
};

// Create the API client instance
const apiClient = createApiClient();

// API methods
export const intentParserApi = {
  /**
   * Parse intent from user message
   */
  parseIntent: async (request: IntentParseRequest): Promise<IntentParseResponse> => {
    const config: AxiosRequestConfig = {
      headers: {
        'X-Tenant-ID': REQUIRED_TENANT_ID, // Fallback protection
      },
    };

    const response = await apiClient.post<IntentParseResponse>(
      '/intent/parse',
      {
        message: request.message,
        user_id: request.user_id,
      },
      config
    );

    return response.data;
  },
};

export const salesAgentApi = {
  /**
   * Send message to sales agent chat
   */
  sendMessage: async (request: SalesAgentRequest): Promise<SalesAgentResponse> => {
    const config: AxiosRequestConfig = {
      headers: {
        'X-Tenant-ID': REQUIRED_TENANT_ID, // Fallback protection
      },
    };

    const response = await apiClient.post<SalesAgentResponse>(
      '/api/sales_agent/chat',
      {
        text: request.text,
        user_id: request.user_id,
      },
      config
    );

    return response.data;
  },
};

// Connection health check
export const healthApi = {
  /**
   * Ping the API to check connection status
   */
  ping: async (): Promise<{ status: string; timestamp: number }> => {
    const config: AxiosRequestConfig = {
      headers: {
        'X-Tenant-ID': REQUIRED_TENANT_ID, // Fallback protection
      },
      timeout: 5000, // Shorter timeout for ping
    };

    const response = await apiClient.get('/ping', config);
    return {
      status: response.data.status || 'ok',
      timestamp: Date.now(),
    };
  },
};
  // TODO: this will be picked from UI store context (React context) once we save it in state after tenant signs in
// Utility function to ensure tenant ID is always included
export const ensureTenantId = (data: Record<string, unknown>): Record<string, unknown> => {
  return {
    ...data,
    tenant_id: data.tenant_id || REQUIRED_TENANT_ID,
  };
};

// Export the configured client for advanced usage
export { apiClient };
export default apiClient;
