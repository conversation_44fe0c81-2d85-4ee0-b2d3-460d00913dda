"""
Cache step implementation for intent parsing orchestration.

This module contains the cache lookup step logic that handles
Redis-based result caching.
"""

import time
import logging
from typing import Dict, Optional, Any, List

from ...intent_utils import (
    log_parsing_result,
    log_general_event,
    calculate_processing_time,
)
from ...response import ParseResponseBuilder
from ...intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class CacheStep:
    """Handles cache lookup step in the orchestration flow."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def execute(
        self,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Execute cache lookup step.

        Args:
            message: User message
            tenant_id: Tenant identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Cached result or None if no cache hit
        """
        cache_start = time.perf_counter()
        
        # Enhanced source chain tracking - show previous step if exists
        if source_chain:
            source_chain.append(f"{source_chain[-1]} → cache")
        else:
            source_chain.append("cache")
        metadata.add_source("cache")

        try:
            cached_result = self.orchestrator.query_cache.get(message, tenant_id)
            metadata.cache_check_time_ms = calculate_processing_time(cache_start)

            if cached_result:
                return self._handle_cache_hit(
                    cached_result, metadata, response_builder, source_chain
                )
            else:
                log_general_event("cache_miss", message=message[:50])
                return None

        except Exception as e:
            return self._handle_cache_error(e, cache_start, metadata, message)

    def _handle_cache_hit(
        self,
        cached_result,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Dict[str, Any]:
        """Handle successful cache hit."""
        metadata.cache_hit = True

        # Performance metrics enhancement integration - FIXED
        # Calculate cache hit ratio from performance tracker
        from core.intent.performance_tracker import get_performance_tracker
        tracker = get_performance_tracker()
        stats = tracker.get_stats()
        if stats["total_requests"] > 0:
            metadata.cache_hit_ratio = stats["cache_hit_rate"]

        # Update response with current session context
        result = response_builder.build_cache_response(
            cached_result.__dict__, metadata.cache_check_time_ms, source_chain
        )

        log_parsing_result(
            "cache_hit",
            cached_result.intent,
            cached_result.confidence,
        )
        return result

    def _handle_cache_error(
        self,
        error: Exception,
        cache_start: float,
        metadata: ParseMetadata,
        message: str,
    ) -> None:
        """Handle cache lookup errors."""
        metadata.add_error(f"Cache lookup failed: {str(error)}")
        metadata.cache_check_time_ms = calculate_processing_time(cache_start)
        log_general_event("cache_error", error=str(error))
        logger.error(f"Cache step failed: {error}")
        return None

    def validate_cache(self) -> Dict[str, Any]:
        """
        Validate cache state.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": {}
        }

        try:
            # Check if cache is available
            if not self.orchestrator.query_cache:
                validation["errors"].append("Query cache not initialized")
                validation["valid"] = False
                return validation

            # Test cache connectivity
            test_key = "health_check_test"
            test_value = {"test": True}
            
            # Try to set and get a test value
            self.orchestrator.query_cache.set(test_key, test_value)
            retrieved = self.orchestrator.query_cache.get(test_key)
            
            if retrieved is None:
                validation["warnings"].append("Cache set/get test failed")
            else:
                # Clean up test key
                self.orchestrator.query_cache.clear_cache()
                validation["info"]["connectivity_test"] = "passed"

            # Get cache stats if available
            if hasattr(self.orchestrator.query_cache, 'get_stats'):
                validation["info"]["stats"] = self.orchestrator.query_cache.get_stats()

        except Exception as e:
            validation["errors"].append(f"Cache validation failed: {e}")
            validation["valid"] = False

        return validation

    def get_debug_info(self, message: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get debug information for cache step.
        
        Args:
            message: Test message
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with debug information
        """
        debug_info = {
            "step": "cache",
            "cache_available": self.orchestrator.query_cache is not None,
            "validation": self.validate_cache(),
        }

        if self.orchestrator.query_cache and message:
            try:
                # Test cache lookup
                start_time = time.perf_counter()
                cached_result = self.orchestrator.query_cache.get(message, tenant_id)
                lookup_time = calculate_processing_time(start_time)

                debug_info["test_lookup"] = {
                    "message": message[:50],
                    "tenant_id": tenant_id,
                    "cache_hit": cached_result is not None,
                    "lookup_time_ms": lookup_time,
                }

                if cached_result:
                    debug_info["test_lookup"]["cached_result"] = {
                        "intent": cached_result.intent,
                        "confidence": cached_result.confidence,
                        "source": getattr(cached_result, 'source', 'unknown'),
                    }

            except Exception as e:
                debug_info["test_lookup"] = {
                    "error": str(e),
                    "message": message[:50],
                }

        return debug_info

    def clear_cache(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Clear cache for debugging purposes.
        
        Args:
            tenant_id: Tenant identifier (None for all tenants)
            
        Returns:
            Dictionary with clear operation results
        """
        try:
            if not self.orchestrator.query_cache:
                return {"error": "Cache not available"}

            cleared_count = self.orchestrator.query_cache.clear_cache(tenant_id)
            
            return {
                "success": True,
                "cleared_count": cleared_count,
                "tenant_id": tenant_id,
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tenant_id": tenant_id,
            }
