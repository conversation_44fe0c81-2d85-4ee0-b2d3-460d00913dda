"""
Error analysis for intent parsing.

This module provides analysis capabilities for error events,
including pattern detection, trend analysis, and reporting.
"""

import time
import logging
from typing import Dict, List, Optional, Any
from collections import defaultdict

from .error_types import ErrorEvent, ErrorCategory, ErrorSeverity
from .error_collector import get_error_collector

logger = logging.getLogger(__name__)


class ErrorAnalyzer:
    """Analyzes error patterns and trends."""

    def __init__(self, error_collector=None):
        """
        Initialize error analyzer.
        
        Args:
            error_collector: Error collector instance (uses global if None)
        """
        self.error_collector = error_collector or get_error_collector()

    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get error summary for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dictionary with error summary
        """
        recent_events = self.error_collector.get_recent_errors(hours)
        
        if not recent_events:
            return {
                "period_hours": hours,
                "total_errors": 0,
                "categories": {},
                "severities": {},
                "components": {},
                "top_errors": [],
                "patterns": {},
                "recovery_stats": {"attempted": 0, "successful": 0}
            }
        
        # Count by category, severity, component
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        component_counts = defaultdict(int)
        error_type_counts = defaultdict(int)
        
        recovery_attempted = 0
        recovery_successful = 0
        
        for event in recent_events:
            category_counts[event.category.value] += 1
            severity_counts[event.severity.value] += 1
            component_counts[event.component] += 1
            error_type_counts[event.error_type] += 1
            
            if event.recovery_attempted:
                recovery_attempted += 1
                if event.recovery_successful:
                    recovery_successful += 1
        
        # Get top errors
        top_errors = sorted(
            error_type_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        # Detect patterns
        patterns = self.error_collector.find_patterns(hours, min_occurrences=3)
        
        return {
            "period_hours": hours,
            "total_errors": len(recent_events),
            "categories": dict(category_counts),
            "severities": dict(severity_counts),
            "components": dict(component_counts),
            "top_errors": top_errors,
            "patterns": patterns,
            "recovery_stats": {
                "attempted": recovery_attempted,
                "successful": recovery_successful,
                "success_rate": recovery_successful / recovery_attempted if recovery_attempted > 0 else 0
            }
        }

    def analyze_error_trends(self, hours: int = 168) -> Dict[str, Any]:  # Default: 1 week
        """
        Analyze error trends over time.
        
        Args:
            hours: Time period to analyze
            
        Returns:
            Dictionary with trend analysis
        """
        recent_events = self.error_collector.get_recent_errors(hours)
        
        if len(recent_events) < 10:
            return {"insufficient_data": True, "event_count": len(recent_events)}
        
        # Group errors by time buckets (hourly)
        time_buckets = defaultdict(int)
        category_trends = defaultdict(lambda: defaultdict(int))
        severity_trends = defaultdict(lambda: defaultdict(int))
        
        current_time = time.time()
        
        for event in recent_events:
            # Calculate hours ago (rounded)
            hours_ago = int((current_time - event.timestamp) / 3600)
            time_buckets[hours_ago] += 1
            category_trends[hours_ago][event.category.value] += 1
            severity_trends[hours_ago][event.severity.value] += 1
        
        # Calculate trend direction
        recent_period = hours // 4  # Last quarter of the period
        recent_errors = sum(
            count for hour, count in time_buckets.items() 
            if hour <= recent_period
        )
        older_errors = sum(
            count for hour, count in time_buckets.items() 
            if hour > recent_period
        )
        
        if older_errors == 0:
            trend_direction = "new"
        elif recent_errors > older_errors * 1.2:
            trend_direction = "increasing"
        elif recent_errors < older_errors * 0.8:
            trend_direction = "decreasing"
        else:
            trend_direction = "stable"
        
        return {
            "period_hours": hours,
            "total_events": len(recent_events),
            "trend_direction": trend_direction,
            "recent_errors": recent_errors,
            "older_errors": older_errors,
            "hourly_distribution": dict(time_buckets),
            "category_trends": {k: dict(v) for k, v in category_trends.items()},
            "severity_trends": {k: dict(v) for k, v in severity_trends.items()},
        }

    def find_error_correlations(self, hours: int = 24) -> Dict[str, Any]:
        """
        Find correlations between different types of errors.
        
        Args:
            hours: Time period to analyze
            
        Returns:
            Dictionary with correlation analysis
        """
        recent_events = self.error_collector.get_recent_errors(hours)
        
        if len(recent_events) < 5:
            return {"insufficient_data": True}
        
        # Group errors by time windows (5-minute buckets)
        time_window = 300  # 5 minutes in seconds
        error_windows = defaultdict(lambda: defaultdict(int))
        
        for event in recent_events:
            window = int(event.timestamp // time_window)
            error_key = f"{event.category.value}:{event.error_type}"
            error_windows[window][error_key] += 1
        
        # Find correlations (errors that occur in the same time windows)
        correlations = defaultdict(lambda: defaultdict(int))
        
        for window, errors in error_windows.items():
            error_types = list(errors.keys())
            for i, error1 in enumerate(error_types):
                for error2 in error_types[i+1:]:
                    correlations[error1][error2] += 1
                    correlations[error2][error1] += 1
        
        # Filter significant correlations (occurred together at least 2 times)
        significant_correlations = {}
        for error1, related_errors in correlations.items():
            significant = {
                error2: count for error2, count in related_errors.items() 
                if count >= 2
            }
            if significant:
                significant_correlations[error1] = significant
        
        return {
            "period_hours": hours,
            "time_windows_analyzed": len(error_windows),
            "correlations": significant_correlations,
        }

    def get_component_health_report(self) -> Dict[str, Any]:
        """
        Generate a health report for each component.
        
        Returns:
            Dictionary with component health analysis
        """
        all_events = list(self.error_collector.error_events)
        
        if not all_events:
            return {"no_data": True}
        
        component_health = {}
        
        # Analyze each component
        for component in self.error_collector.get_component_counts().keys():
            component_errors = self.error_collector.get_errors_by_component(component)
            
            if not component_errors:
                continue
            
            # Calculate health metrics
            total_errors = len(component_errors)
            recent_errors = len([e for e in component_errors if e.is_recent(24)])
            critical_errors = len([e for e in component_errors if e.severity == ErrorSeverity.CRITICAL])
            high_errors = len([e for e in component_errors if e.severity == ErrorSeverity.HIGH])
            
            # Calculate health score (0-100)
            health_score = 100
            health_score -= min(critical_errors * 20, 60)  # Critical errors heavily impact score
            health_score -= min(high_errors * 10, 30)     # High errors moderately impact score
            health_score -= min(recent_errors * 2, 20)    # Recent errors impact score
            health_score = max(health_score, 0)
            
            # Determine health status
            if health_score >= 80:
                health_status = "healthy"
            elif health_score >= 60:
                health_status = "warning"
            elif health_score >= 40:
                health_status = "degraded"
            else:
                health_status = "critical"
            
            component_health[component] = {
                "health_score": health_score,
                "health_status": health_status,
                "total_errors": total_errors,
                "recent_errors_24h": recent_errors,
                "critical_errors": critical_errors,
                "high_errors": high_errors,
                "most_common_error": self._get_most_common_error(component_errors),
            }
        
        return {
            "components": component_health,
            "overall_health": self._calculate_overall_health(component_health),
        }

    def _get_most_common_error(self, errors: List[ErrorEvent]) -> Optional[str]:
        """Get the most common error type for a component."""
        if not errors:
            return None
        
        error_counts = defaultdict(int)
        for error in errors:
            error_counts[error.error_type] += 1
        
        return max(error_counts.items(), key=lambda x: x[1])[0]

    def _calculate_overall_health(self, component_health: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall system health from component health."""
        if not component_health:
            return {"status": "unknown", "score": 0}
        
        scores = [comp["health_score"] for comp in component_health.values()]
        avg_score = sum(scores) / len(scores)
        
        # Overall status is determined by the worst component
        min_score = min(scores)
        
        if min_score >= 80:
            overall_status = "healthy"
        elif min_score >= 60:
            overall_status = "warning"
        elif min_score >= 40:
            overall_status = "degraded"
        else:
            overall_status = "critical"
        
        return {
            "status": overall_status,
            "score": avg_score,
            "worst_component_score": min_score,
            "best_component_score": max(scores),
        }

    def generate_error_report(self, hours: int = 24) -> Dict[str, Any]:
        """
        Generate a comprehensive error report.
        
        Args:
            hours: Time period to analyze
            
        Returns:
            Dictionary with comprehensive error report
        """
        return {
            "report_generated_at": time.time(),
            "analysis_period_hours": hours,
            "summary": self.get_error_summary(hours),
            "trends": self.analyze_error_trends(hours),
            "correlations": self.find_error_correlations(hours),
            "component_health": self.get_component_health_report(),
        }


# Global error analyzer instance
_error_analyzer = ErrorAnalyzer()


def get_error_analyzer() -> ErrorAnalyzer:
    """Get the global error analyzer instance."""
    return _error_analyzer
