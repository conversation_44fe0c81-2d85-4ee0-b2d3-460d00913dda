"""
Error handlers package for intent parsing components.

This package provides specialized error handling for different
components in the intent parsing system.
"""

from .base_handler import ComponentErrorHandler
from .rule_handler import RuleEngineErrorHandler
from .cache_handler import CacheErrorHandler
from .llm_handler import LLMErrorHandler

# Factory function to create appropriate error handler
def create_error_handler(component_type: str) -> ComponentErrorHandler:
    """
    Create appropriate error handler for component type.
    
    Args:
        component_type: Type of component ("rule", "cache", "llm", "clarifier", "orchestration")
        
    Returns:
        Appropriate error handler instance
    """
    handlers = {
        "rule": RuleEngineErrorHandler,
        "cache": CacheErrorHandler,
        "llm": LLMErrorHandler,
    }
    
    handler_class = handlers.get(component_type.lower(), ComponentErrorHandler)
    return handler_class()

# Convenience functions for common error handling patterns
def handle_component_error(
    component_type: str,
    error: Exception,
    **kwargs
) -> Dict[str, Any]:
    """
    Handle error for any component type.
    
    Args:
        component_type: Type of component
        error: The exception that occurred
        **kwargs: Additional context
        
    Returns:
        Error handling result
    """
    handler = create_error_handler(component_type)
    return handler.handle_error(error, **kwargs)

__all__ = [
    "ComponentErrorHandler",
    "RuleEngineErrorHandler",
    "CacheErrorHandler", 
    "LLMErrorHandler",
    "create_error_handler",
    "handle_component_error",
]
