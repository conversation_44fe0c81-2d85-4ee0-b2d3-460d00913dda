import React, { useState, useEffect, useCallback } from 'react';
import { Circle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { healthApi } from '@/api/client';
import { ConnectionStatus } from '@/types/api';

interface ConnectionStatusIndicatorProps {
  className?: string;
  pingInterval?: number; // in milliseconds
}

const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({ 
  className = '',
  pingInterval = 5000 // 5 seconds default
}) => {
  const [status, setStatus] = useState<ConnectionStatus>({
    status: 'checking',
    last_ping: null,
    latency: null,
  });

  const checkConnection = useCallback(async () => {
    const startTime = Date.now();
    
    try {
      setStatus(prev => ({ ...prev, status: 'checking' }));
      
      const response = await healthApi.ping();
      const latency = Date.now() - startTime;
      
      setStatus({
        status: 'connected',
        last_ping: new Date(),
        latency,
      });
    } catch (error) {
      setStatus({
        status: 'disconnected',
        last_ping: new Date(),
        latency: null,
      });
    }
  }, []);

  useEffect(() => {
    // Initial check
    checkConnection();

    // Set up periodic pinging
    const interval = setInterval(checkConnection, pingInterval);

    return () => clearInterval(interval);
  }, [checkConnection, pingInterval]);

  const getStatusColor = () => {
    switch (status.status) {
      case 'connected':
        return 'text-green-500';
      case 'disconnected':
        return 'text-red-500';
      case 'checking':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusText = () => {
    switch (status.status) {
      case 'connected':
        return `Connected${status.latency ? ` (${status.latency.toFixed(2)}ms)` : ''}`;
      case 'disconnected':
        return 'Disconnected';
      case 'checking':
        return 'Checking...';
      default:
        return 'Unknown';
    }
  };

  const getTooltipContent = () => {
    const baseInfo = `Status: ${getStatusText()}`;
    const lastPing = status.last_ping 
      ? `\nLast ping: ${status.last_ping.toLocaleTimeString()}`
      : '';
    
    return baseInfo + lastPing;
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-2 ${className}`}>
            <Circle 
              className={`w-3 h-3 ${getStatusColor()} ${
                status.status === 'checking' ? 'animate-pulse' : ''
              }`}
              fill="currentColor"
            />
            <span className="text-sm text-gray-600">
              {status.status === 'connected' ? 'API' : getStatusText()}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="whitespace-pre-line">{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ConnectionStatusIndicator;
