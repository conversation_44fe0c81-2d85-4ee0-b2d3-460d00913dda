"""
Escalation handler for intent parsing orchestration.

This module handles escalation scenarios when clarifier reaches
maximum rounds or other critical failures occur.
"""

import logging
from typing import Dict, Optional, Any, List

from ...intent_utils import log_general_event
from ...response import ParseResponseBuilder

logger = logging.getLogger(__name__)


class EscalationHandler:
    """Handles escalation scenarios in intent parsing."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def handle_escalation(
        self,
        message: str,
        clarifier_rounds: int,
        source_chain: List[str],
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle escalation when clarifier max rounds reached.

        Args:
            message: Original user message
            clarifier_rounds: Number of clarifier rounds attempted
            source_chain: Source chain for tracking
            session_id: Session identifier
            tenant_id: Tenant identifier

        Returns:
            Escalation response
        """
        source_chain.append("escalation")
        
        response_builder = ParseResponseBuilder(
            session_id=session_id, tenant_id=tenant_id
        )
        
        escalation_result = response_builder.build_escalation_response(
            message, clarifier_rounds, source_chain
        )

        log_general_event(
            "escalation_triggered", 
            message=message[:50], 
            rounds=clarifier_rounds
        )
        
        logger.warning(
            f"Escalation triggered for session {session_id}: "
            f"{clarifier_rounds} clarifier rounds exceeded"
        )
        
        return escalation_result

    def handle_error(
        self,
        error: Exception,
        message: str,
        source_chain: List[str],
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle unexpected errors during parsing.

        Args:
            error: The exception that occurred
            message: Original user message
            source_chain: Source chain for tracking
            session_id: Session identifier
            tenant_id: Tenant identifier

        Returns:
            Error response
        """
        source_chain.append("error")
        
        response_builder = ParseResponseBuilder(
            session_id=session_id, tenant_id=tenant_id
        )
        
        error_result = response_builder.build_error_response(str(error), source_chain)
        
        log_general_event(
            "parsing_error", 
            message=message[:50], 
            error=str(error)
        )
        
        logger.error(f"Intent parsing error for message '{message[:50]}': {error}")
        
        return error_result

    def handle_timeout_escalation(
        self,
        message: str,
        timeout_seconds: float,
        source_chain: List[str],
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle escalation due to processing timeout.

        Args:
            message: Original user message
            timeout_seconds: Timeout duration that was exceeded
            source_chain: Source chain for tracking
            session_id: Session identifier
            tenant_id: Tenant identifier

        Returns:
            Timeout escalation response
        """
        source_chain.append("timeout_escalation")
        
        response_builder = ParseResponseBuilder(
            session_id=session_id, tenant_id=tenant_id
        )
        
        # Build timeout-specific escalation response
        timeout_result = response_builder.build_error_response(
            f"Processing timeout after {timeout_seconds}s", source_chain
        )
        
        # Add timeout-specific metadata
        timeout_result["escalation_reason"] = "timeout"
        timeout_result["timeout_seconds"] = timeout_seconds
        
        log_general_event(
            "timeout_escalation", 
            message=message[:50], 
            timeout=timeout_seconds
        )
        
        logger.warning(
            f"Timeout escalation for session {session_id}: "
            f"Processing exceeded {timeout_seconds}s"
        )
        
        return timeout_result

    def should_escalate(
        self,
        clarifier_rounds: int,
        processing_time_ms: float,
        error_count: int,
    ) -> Dict[str, Any]:
        """
        Determine if escalation should be triggered.

        Args:
            clarifier_rounds: Number of clarifier rounds attempted
            processing_time_ms: Total processing time
            error_count: Number of errors encountered

        Returns:
            Dictionary with escalation decision and reasons
        """
        escalation_reasons = []
        should_escalate = False

        # Check clarifier rounds
        if clarifier_rounds >= 3:
            escalation_reasons.append(f"Max clarifier rounds reached ({clarifier_rounds})")
            should_escalate = True

        # Check processing time (escalate if over 30 seconds)
        if processing_time_ms > 30000:
            escalation_reasons.append(f"Processing time exceeded ({processing_time_ms}ms)")
            should_escalate = True

        # Check error count (escalate if more than 5 errors)
        if error_count > 5:
            escalation_reasons.append(f"Too many errors ({error_count})")
            should_escalate = True

        return {
            "should_escalate": should_escalate,
            "reasons": escalation_reasons,
            "clarifier_rounds": clarifier_rounds,
            "processing_time_ms": processing_time_ms,
            "error_count": error_count,
        }

    def get_escalation_stats(self) -> Dict[str, Any]:
        """
        Get escalation statistics.
        
        Returns:
            Dictionary with escalation statistics
        """
        # This would typically come from performance tracker
        # For now, return basic configuration
        return {
            "max_clarifier_rounds": 3,
            "max_processing_time_ms": 30000,
            "max_error_count": 5,
            "escalation_handler_available": True,
        }
