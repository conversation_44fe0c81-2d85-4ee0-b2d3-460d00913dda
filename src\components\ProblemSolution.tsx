import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { MessageSquareText, AlertTriangle, Layers3 } from 'lucide-react';

const pairs = [
  {
    icon: <MessageSquareText className="h-8 w-8 text-[#14b8a6]" />,
    problem: "Customers are ghosting.",
    solution: "<PERSON><PERSON><PERSON><PERSON> replies instantly like your best salesperson — with nudges, offers, and the perfect SKU."
  },
  {
    icon: <AlertTriangle className="h-8 w-8 text-[#14b8a6]" />,
    problem: "Your team is drowning in support.",
    solution: "<PERSON><PERSON><PERSON><PERSON> handles chats, nudges, and checkout — so your staff doesn’t have to."
  },
  {
    icon: <Layers3 className="h-8 w-8 text-[#14b8a6]" />,
    problem: "Too many tools. No clear picture.",
    solution: "Briskk shows SKUs, orders, sales, and stock — all inside one WhatsApp thread."
  }
];


const ProblemSolution = () => {
  return (
    <section className="py-20 bg-white" id="problem-solution">
      <div className="container mx-auto px-4 max-w-6xl">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-3xl md:text-4xl font-bold mb-10 text-center text-gray-900"
        >
         If You're Selling Online, You're Probably Feeling This Pain.
        </motion.h2>

        <div className="flex flex-col md:flex-row md:space-x-6 space-y-6 md:space-y-0">
          {pairs.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="w-full md:w-1/3 flex-grow bg-white border border-gray-100 rounded-2xl p-6 shadow-md transform transition-transform duration-300 hover:scale-105 hover:shadow-lg"
            >
              <div className="flex items-center gap-3 mb-3">
                {item.icon}
                <p className="text-lg font-medium text-gray-800">{item.problem}</p>
              </div>
              <div className="mt-4 border-t pt-4 text-center">
                <p className="text-base text-teal-700 font-medium italic">“{item.solution}”</p>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="inline-block bg-teal-50 text-teal-700 px-5 py-2 rounded-full text-sm font-medium">
          Briskk turns WhatsApp into your AI-powered storefront — to sell faster, respond smarter, and scale effortlessly.
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSolution;
