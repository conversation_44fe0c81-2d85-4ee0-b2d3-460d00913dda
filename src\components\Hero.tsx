
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Play, ArrowUpRight } from 'lucide-react';

const Hero = () => {
  const [showModal, setShowModal] = useState(false);
  const openGPTLink = () => {
    window.open('https://chat.openai.com/g/g-0XxT0SGAG-briskk-copilot', '_blank');
  };

  return (
    <div className="relative pt-28 pb-16 md:pt-32 md:pb-24 overflow-hidden bg-gradient-to-b from-white to-teal-50">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-gray-900 mb-6 max-w-3xl">
            Smartest Way to Sell on WhatsApp
            </h1>
            
            <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-xl">
  Briskk connects with your <span className="font-semibold text-teal-700">Shopify</span> store and helps you sell more — with AI that talks, nudges, and recommend customers like your best salesperson.
</p>

            
            <div className="flex flex-col sm:flex-row gap-4 mb-10">
              <Button 
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-6 rounded-lg text-lg"
                onClick={() => window.location.href = "#waitlist"}
              >
                Book a Live Demo
              </Button>
              <Button 
  variant="outline" 
  className="border-teal-200 text-teal-700 hover:bg-teal-50 px-8 py-6 rounded-lg text-lg"
  onClick={() => setShowModal(true)}
>
  Talk to the Briskk Co-Pilot
  <ArrowUpRight className="ml-1 h-4 w-4" />
</Button>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-white shadow-xl rounded-2xl overflow-hidden">
              <div className="relative aspect-video bg-gray-100">
                {/* Video placeholder - would be replaced with actual video component */}
                <div className="absolute inset-0 flex items-center justify-center">
                <video
                    controls
                    preload="metadata"
                    className="absolute inset-0 w-full h-full object-cover"
                    poster="/briskk-video-poster.png" // Optional: use a placeholder image
                  >
                    <source src="/briskk-hero-video-april2025.mp4" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
                {/* <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                  <p className="text-white text-sm">Watch how Briskk transforms your WhatsApp into an AI storefront</p>
                </div> */}
              </div>
              
              <div className="p-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">
                  See how Briskk helps D2C brands close more sales and run smarter operations through WhatsApp
                </p>
              </div>
            </div>
            
            <div className="absolute -top-6 -right-6 w-64 h-64 bg-teal-200 rounded-full opacity-30 blur-3xl -z-10"></div>
            <div className="absolute -bottom-8 -left-8 w-72 h-72 bg-blue-200 rounded-full opacity-30 blur-3xl -z-10"></div>
          </motion.div>
        </div>
      </div>
      {showModal && (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div className="bg-white rounded-xl shadow-xl p-6 max-w-sm w-full relative">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">
        Talk to the Briskk Co-Pilot
      </h3>
      <p className="text-gray-600 text-sm mb-6">
        Please fill the form and we’ll share a personalized co-pilot link with you on WhatsApp.
      </p>
      <div className="flex justify-end gap-3">
        <Button variant="ghost" onClick={() => setShowModal(false)}>
          Cancel
        </Button>
        <Button  className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-6 rounded-lg text-lg" onClick={() => {
          setShowModal(false);
          window.location.href = "#waitlist";
        }}>
          Go to Form
        </Button>
      </div>
    </div>
  </div>
)}

    </div>
  );
};

export default Hero;
