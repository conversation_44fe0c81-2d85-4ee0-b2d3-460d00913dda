// 🎨 Redesigned TrustStrip for Briskk.ai with Original Logos

import { motion } from 'framer-motion';
import { ShieldCheck } from 'lucide-react';

const TrustStrip = () => {
  return (
    <section className="bg-[#f9fafb] py-20 border-t border-gray-100">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center max-w-3xl mx-auto"
        >
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-900">
            Your Brand's Data. Your Customers' Trust. Our Priority.
          </h2>
          <p className="text-gray-600 text-base">
            Briskk is built on secure, scalable infrastructure — integrated with Shopify via a live private app and built with WhatsApp-first workflows. API partnership with Meta is in progress.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mt-14 text-center">
          {/* Shopify */}
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="flex flex-col items-center"
          >
            <img src="https://cdn.worldvectorlogo.com/logos/shopify.svg" alt="Shopify" className="w-10 h-10 mb-3" />
            <h3 className="text-lg font-semibold text-gray-900">Shopify</h3>
            <p className="text-sm text-teal-600 mt-1">Private App Live</p>
          </motion.div>

          {/* WhatsApp */}
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.2 }}
            className="flex flex-col items-center"
          >
            <img src="https://upload.wikimedia.org/wikipedia/commons/6/6b/WhatsApp.svg" alt="WhatsApp" className="w-10 h-10 mb-3" />
            <h3 className="text-lg font-semibold text-gray-900">WhatsApp</h3>
            <p className="text-sm text-yellow-600 font-medium">
                Live today via Meta-approved BSPs — direct API coming soon.
            </p>
          </motion.div>

          {/* Security */}
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.3 }}
            className="flex flex-col items-center"
          >
            <ShieldCheck className="w-10 h-10 text-teal-600 mb-3" />
            <h3 className="text-lg font-semibold text-gray-900">Security</h3>
            <p className="text-sm text-teal-700 mt-1">Data Protected by Design</p>
          </motion.div>
        </div>

        <p className="text-sm text-gray-500 text-center mt-10">
          Security-first from day one — so your store can scale without risk.
        </p>
      </div>
    </section>
  );
};

export default TrustStrip;
