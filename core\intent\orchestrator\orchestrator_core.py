"""
Core orchestrator for intent parsing operations.

This module contains the main ParseOrchestrator class that manages the complete
intent detection flow: Rule Engine → Cache → LLM → Clarifier.
"""

import logging
from typing import Dict, Optional, Any

from .orchestrator_components import OrchestrationComponents
from .orchestrator_managers import OrchestrationManagers
from .orchestrator_flow_controller import OrchestrationFlowController

# Session state integration
from core.session.session_state import SessionStore

logger = logging.getLogger(__name__)


class ParseOrchestrator:
    """
    Main orchestrator for intent parsing operations.

    Manages the complete intent detection flow:
    1. Rule Engine (regex matching) - Fast, deterministic
    2. Query Cache (Redis lookup) - Fast, for repeated queries
    3. LLM Parser (GPT-4o/GPT-3.5) - Slower, for complex cases
    4. Clarifier (low confidence) - Interactive clarification system
    """

    def __init__(
        self,
        config_dir: str = "config",
        enable_llm: bool = True,
        enable_clarifier: bool = True,
        session_store: Optional[SessionStore] = None,
    ):
        """
        Initialize the parse orchestrator.

        Args:
            config_dir: Directory containing rule configuration files
            enable_llm: Whether to enable LLM parsing
            enable_clarifier: Whether to enable clarification system
            session_store: Optional SessionStore instance for session state management
        """
        # Initialize components
        self.components = OrchestrationComponents.create(
            config_dir=config_dir,
            enable_llm=enable_llm,
            enable_clarifier=enable_clarifier,
            session_store=session_store,
        )

        # Initialize managers
        self.managers = OrchestrationManagers.create(self)

        # Initialize flow controller
        self.flow_controller = OrchestrationFlowController(self)

        # Expose components for backward compatibility
        self._expose_components()

    def _expose_components(self):
        """Expose components for backward compatibility."""
        self.config_dir = self.components.config_dir
        self.enable_llm = self.components.enable_llm
        self.enable_clarifier = self.components.enable_clarifier
        self.rule_engine = self.components.rule_engine
        self.rules_loader = self.components.rules_loader
        self.query_cache = self.components.query_cache
        self.llm_parser = self.components.llm_parser
        self.clarifier = self.components.clarifier
        self.session_store = self.components.session_store
        self.performance_tracker = self.components.performance_tracker

    # Session management methods - delegated to managers
    def _get_session_context(self, tenant_id: Optional[str], user_id: Optional[str]) -> Dict[str, Any]:
        """Get session context for clarifier round tracking."""
        return self.managers.get_session_context(tenant_id, user_id)

    def _check_clarifier_max_rounds(self, clarifier_rounds: int) -> bool:
        """Check if clarifier has reached maximum rounds (3)."""
        return self.managers.check_clarifier_max_rounds(clarifier_rounds)

    def _reset_clarifier_rounds(self, tenant_id: Optional[str], user_id: Optional[str]) -> None:
        """Reset clarifier rounds to 0 in session context."""
        self.managers.reset_clarifier_rounds(tenant_id, user_id)

    def _increment_clarifier_rounds(self, tenant_id: Optional[str], user_id: Optional[str]) -> None:
        """Increment clarifier rounds in session context."""
        self.managers.increment_clarifier_rounds(tenant_id, user_id)

    def parse_intent(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        locale: str = None,
    ) -> Dict[str, Any]:
        """
        Parse user message to extract intent and entities.

        This is the main entry point that implements the complete flow:
        1. Rule Engine → 2. Cache Check → 3. [LLM] → 4. [Clarifier]

        Args:
            message: User input message
            tenant_id: Tenant identifier for tenant-specific rules
            session_id: Session identifier for conversation tracking
            user_id: User identifier for session state management (uses session_id as fallback)
            locale: Language locale (en, hi, hi-en)

        Returns:
            Dictionary with intent parsing results including source_chain
        """
        # 🔍 INTENTIONAL ERROR FOR TESTING PINPOINT ERROR TRACKING
        # This will demonstrate how the ultra-modular system tracks errors
        # to specific files, functions, and line numbers
        if message == "test_error_tracking":
            # Simulate different types of errors to test error tracking precision
            error_type = tenant_id or "orchestration"

            if error_type == "rule_engine":
                # Simulate rule engine error
                raise ValueError(f"INTENTIONAL RULE ENGINE ERROR in orchestrator_core.py:parse_intent:line_135 - Testing pinpoint error tracking for rule component")
            elif error_type == "cache":
                # Simulate cache error
                raise ConnectionError(f"INTENTIONAL CACHE ERROR in orchestrator_core.py:parse_intent:line_138 - Testing pinpoint error tracking for cache component")
            elif error_type == "llm":
                # Simulate LLM error
                raise TimeoutError(f"INTENTIONAL LLM ERROR in orchestrator_core.py:parse_intent:line_141 - Testing pinpoint error tracking for LLM component")
            elif error_type == "clarifier":
                # Simulate clarifier error
                raise RuntimeError(f"INTENTIONAL CLARIFIER ERROR in orchestrator_core.py:parse_intent:line_144 - Testing pinpoint error tracking for clarifier component")
            else:
                # Simulate orchestration error
                raise Exception(f"INTENTIONAL ORCHESTRATION ERROR in orchestrator_core.py:parse_intent:line_147 - Testing pinpoint error tracking for orchestration component")

        return self.flow_controller.execute_parse_flow(
            message, tenant_id, session_id, user_id, locale
        )

    def get_clarification(
        self,
        intent_result: Dict[str, Any],
        session_id: str,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get clarification prompt for low-confidence intent result."""
        if not self.enable_clarifier or not self.clarifier:
            return {"clarification_needed": False, "message": "Clarifier is disabled"}

        try:
            clarification_result = self.clarifier.get_clarification(
                intent_result, session_id, tenant_id
            )

            return {
                "clarification_needed": clarification_result.clarification_needed,
                "clarification_prompt": clarification_result.clarification_prompt,
                "clarification_round": clarification_result.clarification_round,
                "max_rounds_reached": clarification_result.max_rounds_reached,
                "suggested_intents": clarification_result.suggested_intents,
            }

        except Exception as e:
            logger.error(f"Clarification failed: {e}")
            return {"clarification_needed": False, "error": str(e)}

    def complete_clarification_session(
        self, session_id: str, tenant_id: Optional[str] = None
    ) -> bool:
        """Complete and clean up a clarification session."""
        if not self.enable_clarifier or not self.clarifier:
            return False

        return self.clarifier.complete_session(session_id, tenant_id)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        return self.managers.get_performance_stats(self)

    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check on all components."""
        return self.managers.health_check()

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate orchestrator configuration."""
        return self.managers.validate_configuration()

    def get_diagnostic_info(self) -> Dict[str, Any]:
        """Get comprehensive diagnostic information."""
        return self.managers.get_diagnostic_info()

    def cleanup_resources(self) -> None:
        """Clean up orchestrator resources."""
        self.managers.cleanup_resources(self)
        self.components.cleanup()

    def get_debug_info(self, message: str, tenant_id: str = None) -> Dict[str, Any]:
        """Get debug information for a specific message."""
        return self.managers.get_debug_info(self, message, tenant_id)

    def reload_rules(self) -> bool:
        """Reload rules from configuration files."""
        try:
            return self.rules_loader.check_for_updates(self.rule_engine)
        except Exception as e:
            logger.error(f"Failed to reload rules: {e}")
            return False
