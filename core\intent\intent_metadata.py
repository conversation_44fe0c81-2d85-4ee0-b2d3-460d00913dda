"""
Metadata management for intent parsing.

This module handles debug metadata, performance tracking, and logging
for the intent parsing system.
"""

import time
import logging
from typing import Dict, Optional, Any, List
from dataclasses import dataclass, field
from .intent_utils import calculate_processing_time
from .performance_tracker import get_performance_tracker

logger = logging.getLogger(__name__)


@dataclass
class ParseMetadata:
    """Metadata container for intent parsing operations."""

    # Request context
    message: str
    tenant_id: Optional[str] = None
    session_id: Optional[str] = None
    locale: str = "en"

    # Timing information
    start_time: float = field(default_factory=time.perf_counter)
    processing_time_ms: float = 0.0

    # Source chain tracking
    source_chain: List[str] = field(default_factory=list)
    final_source: str = ""

    # Performance metrics
    rule_check_time_ms: float = 0.0
    cache_check_time_ms: float = 0.0
    llm_call_time_ms: float = 0.0
    clarifier_time_ms: float = 0.0

    # Enhanced metrics collection for token count, LLM latency, and intent hit ratio per source - FIXED
    tokens_used: int = 0
    model_used: Optional[str] = None
    intent_confidence: float = 0.0
    cache_hit_ratio: float = 0.0

    # Result tracking
    rule_matched: bool = False
    cache_hit: bool = False
    llm_called: bool = False
    clarifier_triggered: bool = False

    # Error tracking
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    # Tenant metadata
    tenant_vertical: str = "unknown"
    custom_intents_count: int = 0

    def add_source(self, source: str) -> None:
        """Add a source to the processing chain."""
        self.source_chain.append(source)
        self.final_source = source

    def add_error(self, error: str) -> None:
        """Add an error to the metadata."""
        self.errors.append(error)
        logger.error(f"Parse error: {error}")

    def add_warning(self, warning: str) -> None:
        """Add a warning to the metadata."""
        self.warnings.append(warning)
        logger.warning(f"Parse warning: {warning}")

    def finalize(self) -> None:
        """Finalize metadata by calculating total processing time."""
        self.processing_time_ms = calculate_processing_time(self.start_time)

    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for logging/debugging."""
        return {
            "message_length": len(self.message),
            "tenant_id": self.tenant_id,
            "session_id": self.session_id,
            "locale": self.locale,
            "processing_time_ms": self.processing_time_ms,
            "source_chain": self.source_chain,
            "final_source": self.final_source,
            "rule_check_time_ms": self.rule_check_time_ms,
            "cache_check_time_ms": self.cache_check_time_ms,
            "llm_call_time_ms": self.llm_call_time_ms,
            "clarifier_time_ms": self.clarifier_time_ms,
            "rule_matched": self.rule_matched,
            "cache_hit": self.cache_hit,
            "llm_called": self.llm_called,
            "clarifier_triggered": self.clarifier_triggered,
            "errors": self.errors,
            "warnings": self.warnings,
            "tenant_vertical": self.tenant_vertical,
            "custom_intents_count": self.custom_intents_count,
            # Enhanced metrics
            "tokens_used": self.tokens_used,
            "model_used": self.model_used,
            "intent_confidence": self.intent_confidence,
            "cache_hit_ratio": self.cache_hit_ratio,
        }


# Legacy PerformanceTracker class - now delegates to the new modular tracker
class PerformanceTracker:
    """
    Legacy performance tracker that delegates to the new modular tracker.

    This class maintains backward compatibility while using the new
    performance tracking implementation.
    """

    def __init__(self):
        """Initialize performance tracker."""
        self._tracker = get_performance_tracker()

    def record_request(self, metadata: ParseMetadata) -> None:
        """
        Record a completed request in performance statistics.

        Args:
            metadata: Parse metadata for the completed request
        """
        # Delegate to the new performance tracker
        self._tracker.record_request(
            processing_time_ms=metadata.processing_time_ms,
            final_source=metadata.final_source,
            tenant_id=metadata.tenant_id,
            tenant_vertical=metadata.tenant_vertical,
            custom_intents_count=metadata.custom_intents_count,
            rule_time_ms=metadata.rule_check_time_ms,
            cache_time_ms=metadata.cache_check_time_ms,
            llm_time_ms=metadata.llm_call_time_ms,
            clarifier_time_ms=metadata.clarifier_time_ms,
            cache_hit=metadata.cache_hit,
            rule_matched=metadata.rule_matched,
            llm_called=metadata.llm_called,
            clarifier_triggered=metadata.clarifier_triggered,
            source_chain=metadata.source_chain,
        )

    def get_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        return self._tracker.get_stats()

    def get_detailed_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics including timing breakdowns."""
        return self._tracker.get_detailed_stats()

    def clear_stats(self) -> None:
        """Clear all performance statistics."""
        self._tracker.clear_stats()

    def export_detailed_timings(
        self, limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Export detailed timing data for analysis."""
        return self._tracker.export_detailed_timings(limit)


# Legacy global performance tracker instance for backward compatibility
_legacy_performance_tracker = PerformanceTracker()


def get_legacy_performance_tracker() -> PerformanceTracker:
    """Get the legacy performance tracker instance for backward compatibility."""
    return _legacy_performance_tracker


def create_parse_metadata(
    message: str,
    tenant_id: Optional[str] = None,
    session_id: Optional[str] = None,
    locale: str = "en",
) -> ParseMetadata:
    """
    Create a new parse metadata instance.

    Args:
        message: User message being parsed
        tenant_id: Tenant identifier
        session_id: Session identifier
        locale: Language locale

    Returns:
        New ParseMetadata instance
    """
    return ParseMetadata(
        message=message, tenant_id=tenant_id, session_id=session_id, locale=locale
    )


def log_performance_summary(metadata: ParseMetadata) -> None:
    """
    Log a performance summary for a completed parse operation.

    Args:
        metadata: Completed parse metadata
    """
    logger.info(
        f"Parse completed: {metadata.final_source} source, "
        f"{metadata.processing_time_ms:.2f}ms total, "
        f"chain: {' → '.join(metadata.source_chain)}, "
        f"tenant: {metadata.tenant_id} ({metadata.tenant_vertical})"
    )
