"""
Metadata management for intent parsing.

This module handles debug metadata, performance tracking, and logging
for the intent parsing system.
"""

import time
import logging
from typing import Dict, Optional, Any, List
from dataclasses import dataclass, field
from .intent_config import get_performance_stats_template
from .intent_utils import calculate_processing_time

logger = logging.getLogger(__name__)


@dataclass
class ParseMetadata:
    """Metadata container for intent parsing operations."""

    # Request context
    message: str
    tenant_id: Optional[str] = None
    session_id: Optional[str] = None
    locale: str = "en"

    # Timing information
    start_time: float = field(default_factory=time.perf_counter)
    processing_time_ms: float = 0.0

    # Source chain tracking
    source_chain: List[str] = field(default_factory=list)
    final_source: str = ""

    # Performance metrics
    rule_check_time_ms: float = 0.0
    cache_check_time_ms: float = 0.0
    llm_call_time_ms: float = 0.0
    clarifier_time_ms: float = 0.0

    # Enhanced metrics collection for token count, LLM latency, and intent hit ratio per source - FIXED
    tokens_used: int = 0
    model_used: Optional[str] = None
    intent_confidence: float = 0.0
    cache_hit_ratio: float = 0.0

    # Result tracking
    rule_matched: bool = False
    cache_hit: bool = False
    llm_called: bool = False
    clarifier_triggered: bool = False

    # Error tracking
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    # Tenant metadata
    tenant_vertical: str = "unknown"
    custom_intents_count: int = 0

    def add_source(self, source: str) -> None:
        """Add a source to the processing chain."""
        self.source_chain.append(source)
        self.final_source = source

    def add_error(self, error: str) -> None:
        """Add an error to the metadata."""
        self.errors.append(error)
        logger.error(f"Parse error: {error}")

    def add_warning(self, warning: str) -> None:
        """Add a warning to the metadata."""
        self.warnings.append(warning)
        logger.warning(f"Parse warning: {warning}")

    def finalize(self) -> None:
        """Finalize metadata by calculating total processing time."""
        self.processing_time_ms = calculate_processing_time(self.start_time)

    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for logging/debugging."""
        return {
            "message_length": len(self.message),
            "tenant_id": self.tenant_id,
            "session_id": self.session_id,
            "locale": self.locale,
            "processing_time_ms": self.processing_time_ms,
            "source_chain": self.source_chain,
            "final_source": self.final_source,
            "rule_check_time_ms": self.rule_check_time_ms,
            "cache_check_time_ms": self.cache_check_time_ms,
            "llm_call_time_ms": self.llm_call_time_ms,
            "clarifier_time_ms": self.clarifier_time_ms,
            "rule_matched": self.rule_matched,
            "cache_hit": self.cache_hit,
            "llm_called": self.llm_called,
            "clarifier_triggered": self.clarifier_triggered,
            "errors": self.errors,
            "warnings": self.warnings,
            "tenant_vertical": self.tenant_vertical,
            "custom_intents_count": self.custom_intents_count,
            # Enhanced metrics
            "tokens_used": self.tokens_used,
            "model_used": self.model_used,
            "intent_confidence": self.intent_confidence,
            "cache_hit_ratio": self.cache_hit_ratio,
        }


class PerformanceTracker:
    """Tracks performance statistics for intent parsing operations."""

    def __init__(self):
        """Initialize performance tracker."""
        self.stats = get_performance_stats_template()
        self.detailed_timings: List[Dict[str, Any]] = []

    def record_request(self, metadata: ParseMetadata) -> None:
        """
        Record a completed request in performance statistics.

        Args:
            metadata: Parse metadata for the completed request
        """
        # Update basic counters
        self.stats["total_requests"] += 1
        self.stats["total_time_ms"] += metadata.processing_time_ms

        # Update source-specific counters
        if metadata.rule_matched:
            self.stats["rule_matches"] += 1

        if metadata.cache_hit:
            self.stats["cache_hits"] += 1
        else:
            self.stats["cache_misses"] += 1

        if metadata.llm_called:
            self.stats["llm_requests"] += 1

        if metadata.clarifier_triggered:
            self.stats["clarifier_requests"] += 1

        # Store detailed timing for analysis with enhanced metrics
        self.detailed_timings.append(
            {
                "timestamp": time.time(),
                "tenant_id": metadata.tenant_id,
                "processing_time_ms": metadata.processing_time_ms,
                "source_chain": metadata.source_chain.copy(),
                "final_source": metadata.final_source,
                "rule_time_ms": metadata.rule_check_time_ms,
                "cache_time_ms": metadata.cache_check_time_ms,
                "llm_time_ms": metadata.llm_call_time_ms,
                "clarifier_time_ms": metadata.clarifier_time_ms,
                "tenant_vertical": metadata.tenant_vertical,
                "custom_intents_count": metadata.custom_intents_count,
                # Enhanced metrics tracking
                "tokens_used": metadata.tokens_used,
                "model_used": metadata.model_used,
                "intent_confidence": metadata.intent_confidence,
                "cache_hit_ratio": metadata.cache_hit_ratio,
            }
        )

        # Keep only last 1000 detailed timings to prevent memory growth
        if len(self.detailed_timings) > 1000:
            self.detailed_timings = self.detailed_timings[-1000:]

    def get_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics.

        Returns:
            Dictionary with performance statistics
        """
        stats = self.stats.copy()

        # Calculate derived metrics
        if stats["total_requests"] > 0:
            stats["average_time_ms"] = stats["total_time_ms"] / stats["total_requests"]
            stats["rule_match_rate"] = stats["rule_matches"] / stats["total_requests"]
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["total_requests"]
            stats["llm_usage_rate"] = stats["llm_requests"] / stats["total_requests"]
            stats["clarifier_usage_rate"] = (
                stats["clarifier_requests"] / stats["total_requests"]
            )
        else:
            stats["average_time_ms"] = 0.0
            stats["rule_match_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0
            stats["llm_usage_rate"] = 0.0
            stats["clarifier_usage_rate"] = 0.0

        return stats

    def get_detailed_stats(self) -> Dict[str, Any]:
        """
        Get detailed performance statistics including timing breakdowns.

        Returns:
            Dictionary with detailed performance statistics
        """
        basic_stats = self.get_stats()

        if not self.detailed_timings:
            return basic_stats

        # Calculate timing breakdowns
        total_rule_time = sum(t["rule_time_ms"] for t in self.detailed_timings)
        total_cache_time = sum(t["cache_time_ms"] for t in self.detailed_timings)
        total_llm_time = sum(t["llm_time_ms"] for t in self.detailed_timings)
        total_clarifier_time = sum(
            t["clarifier_time_ms"] for t in self.detailed_timings
        )

        request_count = len(self.detailed_timings)

        detailed_stats = {
            **basic_stats,
            "timing_breakdown": {
                "average_rule_time_ms": total_rule_time / request_count,
                "average_cache_time_ms": total_cache_time / request_count,
                "average_llm_time_ms": total_llm_time / request_count,
                "average_clarifier_time_ms": total_clarifier_time / request_count,
            },
            "source_distribution": self._calculate_source_distribution(),
            "tenant_distribution": self._calculate_tenant_distribution(),
        }

        return detailed_stats

    def _calculate_source_distribution(self) -> Dict[str, int]:
        """Calculate distribution of final sources."""
        distribution = {}
        for timing in self.detailed_timings:
            source = timing["final_source"]
            distribution[source] = distribution.get(source, 0) + 1
        return distribution

    def _calculate_tenant_distribution(self) -> Dict[str, Dict[str, Any]]:
        """Calculate performance distribution by tenant."""
        tenant_stats = {}

        for timing in self.detailed_timings:
            tenant_id = timing["tenant_id"] or "default"

            if tenant_id not in tenant_stats:
                tenant_stats[tenant_id] = {
                    "request_count": 0,
                    "total_time_ms": 0.0,
                    "vertical": timing["tenant_vertical"],
                    "custom_intents_count": timing["custom_intents_count"],
                }

            tenant_stats[tenant_id]["request_count"] += 1
            tenant_stats[tenant_id]["total_time_ms"] += timing["processing_time_ms"]

        # Calculate averages
        for tenant_id, stats in tenant_stats.items():
            if stats["request_count"] > 0:
                stats["average_time_ms"] = (
                    stats["total_time_ms"] / stats["request_count"]
                )
            else:
                stats["average_time_ms"] = 0.0

        return tenant_stats

    def clear_stats(self) -> None:
        """Clear all performance statistics."""
        self.stats = get_performance_stats_template()
        self.detailed_timings.clear()

    def export_detailed_timings(
        self, limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Export detailed timing data for analysis.

        Args:
            limit: Maximum number of timings to export (None for all)

        Returns:
            List of detailed timing dictionaries
        """
        if limit is None:
            return self.detailed_timings.copy()
        else:
            return self.detailed_timings[-limit:].copy()


# Global performance tracker instance
_performance_tracker = PerformanceTracker()


def get_performance_tracker() -> PerformanceTracker:
    """Get the global performance tracker instance."""
    return _performance_tracker


def create_parse_metadata(
    message: str,
    tenant_id: Optional[str] = None,
    session_id: Optional[str] = None,
    locale: str = "en",
) -> ParseMetadata:
    """
    Create a new parse metadata instance.

    Args:
        message: User message being parsed
        tenant_id: Tenant identifier
        session_id: Session identifier
        locale: Language locale

    Returns:
        New ParseMetadata instance
    """
    return ParseMetadata(
        message=message, tenant_id=tenant_id, session_id=session_id, locale=locale
    )


def log_performance_summary(metadata: ParseMetadata) -> None:
    """
    Log a performance summary for a completed parse operation.

    Args:
        metadata: Completed parse metadata
    """
    logger.info(
        f"Parse completed: {metadata.final_source} source, "
        f"{metadata.processing_time_ms:.2f}ms total, "
        f"chain: {' → '.join(metadata.source_chain)}, "
        f"tenant: {metadata.tenant_id} ({metadata.tenant_vertical})"
    )
