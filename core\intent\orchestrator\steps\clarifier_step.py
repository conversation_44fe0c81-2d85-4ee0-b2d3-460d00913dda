"""
Clarifier step implementation for intent parsing orchestration.

This module contains the clarifier step logic that handles
low-confidence intent clarification.
"""

import time
import logging
from typing import Dict, Optional, Any

from ...intent_utils import (
    log_clarifier_event,
    calculate_processing_time,
)
from ...response import ParseResponseBuilder
from ...intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class ClarifierStep:
    """Handles clarifier step in the orchestration flow."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def execute(
        self,
        initial_result: Dict[str, Any],
        session_id: Optional[str],
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
    ) -> Dict[str, Any]:
        """
        Execute clarifier step.

        Args:
            initial_result: Initial parsing result that needs clarification
            session_id: Session identifier
            tenant_id: Tenant identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance

        Returns:
            Updated result with clarifier metadata
        """
        clarifier_start = time.perf_counter()
        metadata.clarifier_triggered = True

        try:
            # Get clarification from clarifier
            clarification_result = self.orchestrator.clarifier.get_clarification(
                initial_result, session_id, tenant_id
            )

            metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)

            # Update response with clarifier metadata
            final_result = response_builder.update_with_clarifier_metadata(
                initial_result, clarification_result
            )

            # Log clarifier events
            self._log_clarifier_events(clarification_result, session_id)

            # Don't cache clarification prompts
            if not clarification_result.clarification_needed:
                self.orchestrator.query_cache.set(
                    initial_result.get("original_message", ""), final_result, tenant_id
                )

            return final_result

        except Exception as e:
            return self._handle_clarifier_error(
                e, clarifier_start, metadata, initial_result
            )

    def _log_clarifier_events(self, clarification_result, session_id: Optional[str]):
        """Log appropriate clarifier events."""
        if clarification_result.max_rounds_reached:
            log_clarifier_event("max_rounds", session_id=session_id)
        elif clarification_result.clarification_needed:
            log_clarifier_event(
                "prompt_generated",
                session_id=session_id,
                round=clarification_result.clarification_round,
            )
        else:
            log_clarifier_event("not_needed", session_id=session_id)

    def _handle_clarifier_error(
        self,
        error: Exception,
        clarifier_start: float,
        metadata: ParseMetadata,
        initial_result: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Handle clarifier step errors."""
        metadata.add_error(f"Clarifier step failed: {str(error)}")
        metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)
        logger.error(f"Clarifier step failed: {error}")
        return initial_result

    def validate_clarifier(self) -> Dict[str, Any]:
        """
        Validate clarifier state.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": {}
        }

        try:
            # Check if clarifier is enabled and available
            if not self.orchestrator.enable_clarifier:
                validation["info"]["clarifier_enabled"] = False
                return validation

            if not self.orchestrator.clarifier:
                validation["errors"].append("Clarifier enabled but not initialized")
                validation["valid"] = False
                return validation

            validation["info"]["clarifier_enabled"] = True

            # Check if clarifier has required components
            if hasattr(self.orchestrator.clarifier, 'redis_client'):
                validation["info"]["redis_available"] = True
            else:
                validation["warnings"].append("Clarifier Redis client not available")

            # Test clarifier functionality with a mock result
            test_result = {
                "intent": "test",
                "confidence": 0.5,
                "entities": {},
                "source": "test"
            }
            
            # This should not fail for a healthy clarifier
            clarification = self.orchestrator.clarifier.get_clarification(
                test_result, "health_check_session", "test_tenant"
            )
            
            validation["info"]["functionality_test"] = {
                "success": True,
                "clarification_needed": clarification.clarification_needed
            }

        except Exception as e:
            validation["errors"].append(f"Clarifier validation failed: {e}")
            validation["valid"] = False

        return validation

    def get_debug_info(
        self, 
        test_result: Optional[Dict[str, Any]] = None,
        session_id: str = "debug_session",
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get debug information for clarifier step.
        
        Args:
            test_result: Test result to use for clarification
            session_id: Session identifier for testing
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with debug information
        """
        debug_info = {
            "step": "clarifier",
            "clarifier_enabled": self.orchestrator.enable_clarifier,
            "clarifier_available": self.orchestrator.clarifier is not None,
            "validation": self.validate_clarifier(),
        }

        if (self.orchestrator.enable_clarifier and 
            self.orchestrator.clarifier and 
            test_result):
            try:
                # Test clarifier functionality
                start_time = time.perf_counter()
                clarification_result = self.orchestrator.clarifier.get_clarification(
                    test_result, session_id, tenant_id
                )
                clarification_time = calculate_processing_time(start_time)

                debug_info["test_clarification"] = {
                    "test_result": test_result,
                    "session_id": session_id,
                    "tenant_id": tenant_id,
                    "clarification_time_ms": clarification_time,
                    "clarification_needed": clarification_result.clarification_needed,
                    "clarification_round": clarification_result.clarification_round,
                    "max_rounds_reached": clarification_result.max_rounds_reached,
                }

                if clarification_result.clarification_needed:
                    debug_info["test_clarification"]["clarification_prompt"] = (
                        clarification_result.clarification_prompt[:100] + "..."
                        if len(clarification_result.clarification_prompt) > 100
                        else clarification_result.clarification_prompt
                    )

            except Exception as e:
                debug_info["test_clarification"] = {
                    "error": str(e),
                    "test_result": test_result,
                }

        return debug_info

    def complete_session(
        self, 
        session_id: str, 
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete a clarification session for debugging.
        
        Args:
            session_id: Session identifier
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with completion results
        """
        try:
            if not self.orchestrator.enable_clarifier or not self.orchestrator.clarifier:
                return {"error": "Clarifier not available"}

            success = self.orchestrator.clarifier.complete_session(session_id, tenant_id)
            
            return {
                "success": success,
                "session_id": session_id,
                "tenant_id": tenant_id,
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id,
                "tenant_id": tenant_id,
            }
