"""
Data models for rule-based intent detection.

This module contains the data classes and models used by the rule engine.
"""

import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class IntentRule:
    """Represents a single intent detection rule."""

    id: str
    intent: str
    patterns: List[str]
    entities: Dict[str, Dict[str, Any]]
    locale: List[str]
    priority: int
    tenant_id: Optional[str] = None
    compiled_patterns: Optional[List[re.Pattern]] = None
    source: str = (
        "base"  # "base" or "tenant" - tracks rule source for conflict detection
    )


@dataclass
class RuleMatch:
    """Represents a successful rule match result."""

    rule_id: str
    intent: str
    confidence: float
    entities: Dict[str, Any]
    pattern_matched: str
    processing_time_ms: float
    source: str  # "base" or "tenant" for observability
    # Rule reasoning field to explain why specific rules matched - FIXED
    reasoning: str = ""  # Explanation of why this rule matched


@dataclass
class RuleConflict:
    """Represents a conflict between rules."""

    rule_id: str
    conflicting_rule_id: str
    conflict_type: str  # "pattern_overlap", "priority_conflict", "intent_mismatch"
    description: str
    severity: str  # "warning", "error"


@dataclass
class RuleValidationResult:
    """Represents the result of rule validation."""

    valid: bool
    errors: List[str]
    warnings: List[str]
    conflicts: List[RuleConflict]
    rule_count: int
    tenant_rule_count: int = 0
