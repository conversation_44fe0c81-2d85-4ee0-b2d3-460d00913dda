"""
Error type definitions for intent parsing.

This module defines the error categories, severity levels, and
error event data structures used throughout the system.
"""

import time
from typing import Dict, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class ErrorCategory(Enum):
    """Categories of errors in the intent parsing system."""
    RULE_ENGINE = "rule_engine"
    CACHE = "cache"
    LLM = "llm"
    CLARIFIER = "clarifier"
    SESSION = "session"
    ORCHESTRATION = "orchestration"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    UNKNOWN = "unknown"


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    CRITICAL = "critical"  # System cannot function
    HIGH = "high"         # Major functionality impacted
    MEDIUM = "medium"     # Some functionality impacted
    LOW = "low"          # Minor issues, system still functional
    INFO = "info"        # Informational, not really an error


@dataclass
class ErrorEvent:
    """Represents a single error event."""
    timestamp: float
    category: ErrorCategory
    severity: ErrorSeverity
    component: str
    error_type: str
    message: str
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    tenant_id: Optional[str] = None
    session_id: Optional[str] = None
    user_message: Optional[str] = None
    recovery_attempted: bool = False
    recovery_successful: bool = False

    @classmethod
    def create(
        cls,
        category: ErrorCategory,
        severity: ErrorSeverity,
        component: str,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        tenant_id: Optional[str] = None,
        session_id: Optional[str] = None,
        user_message: Optional[str] = None,
        recovery_attempted: bool = False,
        recovery_successful: bool = False,
    ) -> "ErrorEvent":
        """
        Create an ErrorEvent from an exception.
        
        Args:
            category: Error category
            severity: Error severity
            component: Component where error occurred
            error: The exception that occurred
            context: Additional context information
            tenant_id: Tenant identifier
            session_id: Session identifier
            user_message: User message that caused the error
            recovery_attempted: Whether recovery was attempted
            recovery_successful: Whether recovery was successful
            
        Returns:
            New ErrorEvent instance
        """
        return cls(
            timestamp=time.time(),
            category=category,
            severity=severity,
            component=component,
            error_type=type(error).__name__,
            message=str(error),
            context=context or {},
            stack_trace=cls._get_stack_trace(error),
            tenant_id=tenant_id,
            session_id=session_id,
            user_message=user_message[:100] if user_message else None,
            recovery_attempted=recovery_attempted,
            recovery_successful=recovery_successful,
        )

    @staticmethod
    def _get_stack_trace(error: Exception) -> Optional[str]:
        """Get stack trace from exception."""
        import traceback
        try:
            return traceback.format_exc()
        except Exception:
            return None

    def to_dict(self) -> Dict[str, Any]:
        """Convert error event to dictionary."""
        return {
            "timestamp": self.timestamp,
            "category": self.category.value,
            "severity": self.severity.value,
            "component": self.component,
            "error_type": self.error_type,
            "message": self.message,
            "context": self.context,
            "tenant_id": self.tenant_id,
            "session_id": self.session_id,
            "user_message": self.user_message,
            "recovery_attempted": self.recovery_attempted,
            "recovery_successful": self.recovery_successful,
        }

    def get_log_level(self) -> str:
        """Get appropriate log level for this error."""
        if self.severity == ErrorSeverity.CRITICAL:
            return "critical"
        elif self.severity == ErrorSeverity.HIGH:
            return "error"
        elif self.severity == ErrorSeverity.MEDIUM:
            return "warning"
        else:
            return "info"

    def get_short_description(self) -> str:
        """Get short description for logging."""
        return (
            f"[{self.category.value.upper()}] {self.component}: "
            f"{self.error_type} - {self.message[:50]}..."
        )

    def is_recent(self, hours: int = 1) -> bool:
        """Check if error occurred within specified hours."""
        cutoff_time = time.time() - (hours * 3600)
        return self.timestamp >= cutoff_time

    def matches_pattern(self, pattern: Dict[str, Any]) -> bool:
        """
        Check if error matches a specific pattern.
        
        Args:
            pattern: Dictionary with pattern criteria
            
        Returns:
            True if error matches pattern
        """
        for key, value in pattern.items():
            if key == "category" and self.category != value:
                return False
            elif key == "severity" and self.severity != value:
                return False
            elif key == "component" and self.component != value:
                return False
            elif key == "error_type" and self.error_type != value:
                return False
            elif key == "tenant_id" and self.tenant_id != value:
                return False
        
        return True
