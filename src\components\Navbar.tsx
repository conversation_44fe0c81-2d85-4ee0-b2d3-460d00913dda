import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { motion } from 'framer-motion';
import { Menu, X } from 'lucide-react';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out py-5 px-6 ${
        isScrolled
          ? "bg-white/90 backdrop-blur-md shadow-md"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between">
      <a href="/" className="flex items-center gap-1 hover:opacity-80 transition-opacity">
          <span className="font-bold text-2xl">
            <span className="text-blue-600">briskk</span>
            <span className="text-teal-500">.ai</span>
          </span>

        </a>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <a href="#demos" className="text-sm font-medium text-gray-700 hover:text-teal-600 transition-colors">
            See Demos
          </a>
          <a href="#agents" className="text-sm font-medium text-gray-700 hover:text-teal-600 transition-colors">
            AI Agents
          </a>
          <a href="/agent-debug-tool" className="text-sm font-medium text-gray-700 hover:text-teal-600 transition-colors">
            Debug Panel
          </a>
          <Button
            className="bg-teal-600 hover:bg-teal-700 text-white"
            onClick={() => window.location.href = "#waitlist"}
          >
            Book a Live Demo
          </Button>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden focus:outline-none"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6 text-gray-700" />
          ) : (
            <Menu className="h-6 w-6 text-gray-700" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="md:hidden absolute top-16 left-0 right-0 bg-white shadow-lg"
        >
          <div className="px-4 py-5">
            <nav className="flex flex-col space-y-4 mb-6">
              <a
                href="#demos"
                className="text-base font-medium px-4 py-2 hover:bg-teal-50 hover:text-teal-600 rounded-md"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                See Demos
              </a>
              <a
                href="#agents"
                className="text-base font-medium px-4 py-2 hover:bg-teal-50 hover:text-teal-600 rounded-md"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                AI Agents
              </a>
              <a
                href="/agent-debug-tool"
                className="text-base font-medium px-4 py-2 hover:bg-teal-50 hover:text-teal-600 rounded-md"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Debug Panel
              </a>
            </nav>
            <Button
              className="w-full bg-teal-600 hover:bg-teal-700 text-white"
              onClick={() => {
                window.location.href = "#waitlist";
                setIsMobileMenuOpen(false);
              }}
            >
              Book a Live Demo
            </Button>
          </div>
        </motion.div>
      )}
    </header>
  );
};

export default Navbar;
