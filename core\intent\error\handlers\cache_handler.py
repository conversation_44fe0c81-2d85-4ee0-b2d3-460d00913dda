"""
Cache error handler for intent parsing.

This module provides specialized error handling for cache
components and operations.
"""

import logging
from typing import Dict, Optional, Any

from .base_handler import ComponentErrorHandler
from ..error_types import ErrorSeverity

logger = logging.getLogger(__name__)


class CacheError<PERSON>andler(ComponentErrorHandler):
    """Specialized error handler for cache components."""

    def __init__(self, error_collector=None):
        super().__init__("cache", error_collector)

    def handle_cache_connection_error(
        self,
        error: Exception,
        operation: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle cache connection errors."""
        context = {
            "operation": operation,
            "error_type": "cache_connection"
        }
        
        def recovery():
            # Attempt to reconnect or use fallback
            logger.info(f"Attempting cache connection recovery for {operation}")
            return None
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.LOW,  # Cache failures are not critical
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def handle_cache_operation_error(
        self,
        error: Exception,
        operation: str,
        key: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle cache operation errors."""
        context = {
            "operation": operation,
            "cache_key": key,
            "error_type": "cache_operation"
        }
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.LOW,
            tenant_id=tenant_id
        )

    def handle_cache_serialization_error(
        self,
        error: Exception,
        data_type: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle cache serialization errors."""
        context = {
            "data_type": data_type,
            "error_type": "cache_serialization"
        }
        
        def recovery():
            # Attempt to use alternative serialization
            logger.info(f"Attempting cache serialization recovery for {data_type}")
            return {"action": "skip_cache", "reason": "serialization_failed"}
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def handle_cache_timeout_error(
        self,
        error: Exception,
        operation: str,
        timeout_seconds: float,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle cache timeout errors."""
        context = {
            "operation": operation,
            "timeout_seconds": timeout_seconds,
            "error_type": "cache_timeout"
        }
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.LOW,
            tenant_id=tenant_id
        )

    def get_cache_error_patterns(self) -> Dict[str, Any]:
        """
        Get common cache error patterns.
        
        Returns:
            Dictionary with cache error pattern analysis
        """
        cache_errors = self.error_collector.get_errors_by_component(self.component_name)
        
        patterns = {
            "connection_errors": 0,
            "operation_errors": 0,
            "serialization_errors": 0,
            "timeout_errors": 0,
            "operation_distribution": {},
            "common_patterns": []
        }
        
        for error in cache_errors:
            error_type = error.context.get("error_type", "unknown")
            operation = error.context.get("operation", "unknown")
            
            if error_type == "cache_connection":
                patterns["connection_errors"] += 1
            elif error_type == "cache_operation":
                patterns["operation_errors"] += 1
            elif error_type == "cache_serialization":
                patterns["serialization_errors"] += 1
            elif error_type == "cache_timeout":
                patterns["timeout_errors"] += 1
            
            # Track operation distribution
            patterns["operation_distribution"][operation] = patterns["operation_distribution"].get(operation, 0) + 1
        
        # Identify common patterns
        if patterns["connection_errors"] > 10:
            patterns["common_patterns"].append("Frequent cache connection issues")
        
        if patterns["timeout_errors"] > 5:
            patterns["common_patterns"].append("Cache timeout issues")
        
        if patterns["serialization_errors"] > 3:
            patterns["common_patterns"].append("Data serialization problems")
        
        return patterns

    def validate_cache_health(self) -> Dict[str, Any]:
        """
        Validate cache specific health.
        
        Returns:
            Dictionary with cache health validation
        """
        base_validation = self.validate_component_health()
        cache_patterns = self.get_cache_error_patterns()
        
        # Add cache-specific health checks
        if cache_patterns["connection_errors"] > 10:
            base_validation["warnings"].append("Frequent cache connection failures")
            base_validation["health_score"] -= 15
        
        if cache_patterns["timeout_errors"] > 5:
            base_validation["warnings"].append("Cache timeout issues")
            base_validation["health_score"] -= 10
        
        # Cache errors are generally not critical, so don't mark as unhealthy
        # unless there are excessive errors
        total_errors = sum([
            cache_patterns["connection_errors"],
            cache_patterns["operation_errors"],
            cache_patterns["serialization_errors"],
            cache_patterns["timeout_errors"]
        ])
        
        if total_errors > 50:
            base_validation["warnings"].append("Excessive cache errors may impact performance")
            base_validation["health_score"] -= 20
        
        base_validation["cache_patterns"] = cache_patterns
        base_validation["health_score"] = max(base_validation["health_score"], 0)
        
        return base_validation
