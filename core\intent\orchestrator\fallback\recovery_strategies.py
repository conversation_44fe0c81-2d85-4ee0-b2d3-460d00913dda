"""
Error recovery strategies for intent parsing orchestration.

This module implements specific recovery strategies for different
types of component failures.
"""

import logging
from typing import Dict, Optional, Any

from ...intent_config import FALLBACK_INTENT
from ...intent_utils import log_general_event
from ...response import ParseResponseBuilder

logger = logging.getLogger(__name__)


class ErrorRecoveryStrategy:
    """Implements error recovery strategies for different failure scenarios."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def recover_from_rule_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """
        Attempt recovery from rule engine failure.

        Args:
            message: User message
            tenant_id: Tenant identifier
            error: The rule engine error

        Returns:
            Recovery result or None if recovery not possible
        """
        logger.warning(f"Attempting recovery from rule failure: {error}")
        
        # Try to reload rules if it's a rule loading error
        if "rule" in str(error).lower() and "load" in str(error).lower():
            try:
                if self.orchestrator.reload_rules():
                    logger.info("Rules reloaded successfully during recovery")
                    # Don't retry immediately, let the flow continue to next step
                    return None
            except Exception as reload_error:
                logger.error(f"Rule reload during recovery failed: {reload_error}")
        
        return None

    def recover_from_cache_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """
        Attempt recovery from cache failure.

        Args:
            message: User message
            tenant_id: Tenant identifier
            error: The cache error

        Returns:
            Recovery result or None if recovery not possible
        """
        logger.warning(f"Attempting recovery from cache failure: {error}")
        
        # Cache failures are not critical - continue without cache
        # Log the issue for monitoring
        log_general_event("cache_recovery", error=str(error))
        
        return None

    def recover_from_llm_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """
        Attempt recovery from LLM failure.

        Args:
            message: User message
            tenant_id: Tenant identifier
            error: The LLM error

        Returns:
            Recovery result or None if recovery not possible
        """
        logger.warning(f"Attempting recovery from LLM failure: {error}")
        
        # For LLM failures, we can try fallback model if available
        if hasattr(self.orchestrator.llm_parser, 'try_fallback_model'):
            try:
                fallback_result = self.orchestrator.llm_parser.try_fallback_model(
                    message, tenant_id
                )
                if fallback_result:
                    logger.info("LLM fallback model recovery successful")
                    return fallback_result
            except Exception as fallback_error:
                logger.error(f"LLM fallback recovery failed: {fallback_error}")
        
        return None

    def recover_from_clarifier_failure(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        error: Exception,
    ) -> Optional[Dict[str, Any]]:
        """
        Attempt recovery from clarifier failure.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            error: The clarifier error

        Returns:
            Recovery result or None if recovery not possible
        """
        logger.warning(f"Attempting recovery from clarifier failure: {error}")
        
        # For clarifier failures, we can return a simplified response
        # without clarification capabilities
        try:
            response_builder = ParseResponseBuilder(
                session_id=session_id, tenant_id=tenant_id
            )
            
            recovery_result = response_builder.build_fallback_response(
                FALLBACK_INTENT, ["clarifier_recovery"], message
            )
            
            # Add recovery metadata
            recovery_result["recovery_attempted"] = True
            recovery_result["original_error"] = str(error)
            
            logger.info("Clarifier recovery response generated")
            return recovery_result
            
        except Exception as recovery_error:
            logger.error(f"Clarifier recovery failed: {recovery_error}")
        
        return None

    def get_recovery_capabilities(self) -> Dict[str, Any]:
        """
        Get available recovery capabilities.
        
        Returns:
            Dictionary with recovery capabilities
        """
        capabilities = {
            "rule_recovery": {
                "available": True,
                "strategies": ["rule_reload"]
            },
            "cache_recovery": {
                "available": True,
                "strategies": ["continue_without_cache"]
            },
            "llm_recovery": {
                "available": hasattr(self.orchestrator.llm_parser, 'try_fallback_model'),
                "strategies": ["fallback_model"] if hasattr(self.orchestrator.llm_parser, 'try_fallback_model') else []
            },
            "clarifier_recovery": {
                "available": True,
                "strategies": ["simplified_response"]
            }
        }
        
        return capabilities

    def test_recovery_strategies(self) -> Dict[str, Any]:
        """
        Test all recovery strategies.
        
        Returns:
            Dictionary with test results
        """
        test_results = {}
        
        # Test rule recovery
        try:
            test_error = Exception("Test rule error")
            result = self.recover_from_rule_failure("test", "test_tenant", test_error)
            test_results["rule_recovery"] = {"success": True, "result": result}
        except Exception as e:
            test_results["rule_recovery"] = {"success": False, "error": str(e)}
        
        # Test cache recovery
        try:
            test_error = Exception("Test cache error")
            result = self.recover_from_cache_failure("test", "test_tenant", test_error)
            test_results["cache_recovery"] = {"success": True, "result": result}
        except Exception as e:
            test_results["cache_recovery"] = {"success": False, "error": str(e)}
        
        # Test LLM recovery
        try:
            test_error = Exception("Test LLM error")
            result = self.recover_from_llm_failure("test", "test_tenant", test_error)
            test_results["llm_recovery"] = {"success": True, "result": result}
        except Exception as e:
            test_results["llm_recovery"] = {"success": False, "error": str(e)}
        
        # Test clarifier recovery
        try:
            test_error = Exception("Test clarifier error")
            result = self.recover_from_clarifier_failure("test", "test_tenant", "test_session", test_error)
            test_results["clarifier_recovery"] = {"success": True, "result": result}
        except Exception as e:
            test_results["clarifier_recovery"] = {"success": False, "error": str(e)}
        
        return test_results
