
import React from 'react';
import { Linkedin, Twitter, Instagram, Mail, Phone } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
          {/* Column 1: Brand Info */}
          <div>
            <div className="mb-3">
              <span className="font-extrabold text-blue-600 tracking-tight text-xl">briskk</span>
              <span className="text-teal-500 font-semibold text-xl">.ai</span>
            </div>
            <p className="text-gray-400 mb-4"><PERSON>tail co-pilot</p>
            <div className="flex flex-col space-y-2 text-gray-400">
              <a href="mailto:<EMAIL>" className="flex items-center hover:text-teal-400 transition-colors">
                <Mail className="h-4 w-4 mr-2" />
                <EMAIL>
              </a>
              <a href="tel:+919019897582" className="flex items-center hover:text-teal-400 transition-colors">
                <Phone className="h-4 w-4 mr-2" />
                +91 9019897582
              </a>
            </div>
          </div>
          
          {/* Column 2: Social Media */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Connect With Us</h3>
            <div className="flex space-x-5">
              <a href="https://www.linkedin.com/company/briskk" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-teal-400 transition-colors">
                <Linkedin className="h-7 w-7" />
                <span className="sr-only">LinkedIn</span>
              </a>
              <a href="https://x.com/Briskk_shop" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-teal-400 transition-colors">
                <Twitter className="h-7 w-7" />
                <span className="sr-only">Twitter</span>
              </a>
              <a href="https://www.instagram.com/briskk_shop/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-teal-400 transition-colors">
                <Instagram className="h-7 w-7" />
                <span className="sr-only">Instagram</span>
              </a>
            </div>
          </div>
          
          {/* Column 3: Legal */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><a href="https://store.briskk.one/privacy-policy"  target="_blank" 
                rel="noopener noreferrer" className="text-gray-400 hover:text-teal-400 transition-colors">Privacy Policy</a></li>
                            <li><a href="https://store.briskk.one/terms-condition"  target="_blank" 
                rel="noopener noreferrer" className="text-gray-400 hover:text-teal-400 transition-colors">Terms of Service</a></li>
            </ul>
          </div>
        </div>
        
        <Separator className="bg-gray-800 my-8" />
        
        {/* Trust Badges */}
        <div className="text-center space-y-4">
          <div className="flex flex-wrap justify-center gap-4 text-gray-400">
            <span className="flex items-center">🏆 Startup India – Recognized Startup</span>
            <span className="hidden md:flex items-center">•</span>
            <span className="flex items-center">🛡️ Razorpay Rize – Verified Partner</span>
            <span className="hidden md:flex items-center">•</span>
            <span className="flex items-center">🇮🇳 Made with <span className="text-teal-500 mx-1">💚</span> in Bharat</span>
          </div>
          
          <div className="text-sm text-gray-500">
            <p>CIN: U62013KA2024PTC185809 • DPIIT: DIPP173574</p>
            <p><strong>Briskk is a product of ChannelBlend Technologies Pvt. Ltd., a DPIIT-recognized startup based in Bangalore, India.</strong></p>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-6">
          <p className="text-sm text-gray-500 text-center">
            © {new Date().getFullYear()} Briskk.ai by ChannelBlend Technologies Pvt. Ltd.  All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
