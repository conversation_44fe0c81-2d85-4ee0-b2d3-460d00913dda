import { useState, useCallback } from 'react';
import { salesAgentApi } from '@/api/client';
import { SalesAgentRequest, SalesAgentResponse, DebugInfo, ApiError } from '@/types/api';

interface UseSalesAgentReturn {
  sendMessage: (request: SalesAgentRequest) => Promise<SalesAgentResponse | null>;
  isLoading: boolean;
  error: string | null;
  debugInfo: DebugInfo;
  clearError: () => void;
}

// Exponential backoff retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 8000,  // 8 seconds
};

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const calculateDelay = (attempt: number): number => {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(2, attempt);
  return Math.min(delay, RETRY_CONFIG.maxDelay);
};

export const useSalesAgent = (): UseSalesAgentReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    request_status: 'idle',
    response_time: 0,
  });

  const sendMessage = useCallback(async (request: SalesAgentRequest): Promise<SalesAgentResponse | null> => {
    setIsLoading(true);
    setError(null);
    
    const startTime = Date.now();
    
    setDebugInfo({
      request_status: 'loading',
      response_time: 0,
    });

    let lastError: ApiError | null = null;

    // Retry logic with exponential backoff
    for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {
      try {
        const response = await salesAgentApi.sendMessage(request);
        const responseTime = Date.now() - startTime;

        // Update debug info with successful response
        setDebugInfo({
          request_status: 'success',
          response_time: responseTime,
          parsed_intent: response.content.metadata.detected_intent,
          confidence_score: 1.0, // Sales agent doesn't return confidence, assume high
          source_chain: response.content.metadata.source,
          tool_execution_status: response.status === 'success' ? 'success' : 'error',
          cache_status: 'miss', // Sales agent typically doesn't use cache
          fallback_used: false,
          processing_latency: responseTime,
          raw_response: response,
        });

        setIsLoading(false);
        return response;

      } catch (err) {
        lastError = err as ApiError;
        
        // If this is the last attempt, don't wait
        if (attempt === RETRY_CONFIG.maxRetries) {
          break;
        }

        // Wait before retrying (exponential backoff)
        const delay = calculateDelay(attempt);
        await sleep(delay);
      }
    }

    // All retries failed
    const responseTime = Date.now() - startTime;
    const errorMessage = lastError?.message || 'Failed to send message after multiple attempts';
    
    setError(errorMessage);
    setDebugInfo({
      request_status: 'error',
      response_time: responseTime,
      error_message: errorMessage,
      tool_execution_status: 'error',
    });
    
    setIsLoading(false);
    return null;
  }, []);

  const clearError = useCallback(() => {
    setError(null);
    setDebugInfo(prev => ({
      ...prev,
      request_status: 'idle',
      error_message: undefined,
    }));
  }, []);

  return {
    sendMessage,
    isLoading,
    error,
    debugInfo,
    clearError,
  };
};
