import React from 'react';
import { DebugLog } from '@/hooks/useDebugLogFetcher';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Zap,
  Target,
  MessageSquare,
  Settings
} from 'lucide-react';

interface DebugSidebarProps {
  debugLog: DebugLog | null;
  isLoading: boolean;
}

const DebugSidebar: React.FC<DebugSidebarProps> = ({ debugLog, isLoading }) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="w-8 h-8 border-4 border-teal-200 border-t-teal-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-sm text-gray-600">Loading debug information...</p>
        </div>
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-6 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!debugLog) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">No debug data available</p>
        </CardContent>
      </Card>
    );
  }

  const debugItems = [
    {
      icon: <MessageSquare className="w-4 h-4 text-blue-500" />,
      label: 'User Input',
      value: debugLog.input,
      type: 'text' as const
    },
    {
      icon: <Target className="w-4 h-4 text-purple-500" />,
      label: 'Parsed Intent',
      value: debugLog.parsed_intent,
      type: 'badge' as const,
      badgeVariant: 'secondary' as const
    },
    {
      icon: <Settings className="w-4 h-4 text-orange-500" />,
      label: 'Tool Called',
      value: debugLog.tool_called,
      type: 'badge' as const,
      badgeVariant: 'outline' as const
    },
    {
      icon: debugLog.tool_success ?
        <CheckCircle className="w-4 h-4 text-green-500" /> :
        <XCircle className="w-4 h-4 text-red-500" />,
      label: 'Tool Success',
      value: debugLog.tool_success,
      type: 'boolean' as const
    },
    {
      icon: <Database className="w-4 h-4 text-indigo-500" />,
      label: 'Cache Hit',
      value: debugLog.cache_hit,
      type: 'boolean' as const
    },
    {
      icon: <Zap className="w-4 h-4 text-yellow-500" />,
      label: 'Fallback Used',
      value: debugLog.fallback_used,
      type: 'boolean' as const
    },
    {
      icon: <Clock className="w-4 h-4 text-gray-500" />,
      label: 'Latency',
      value: `${debugLog.latency.toFixed(5)}s`,
      type: 'metric' as const
    }
  ];

  // Add vector fallback score if it exists
  if (debugLog.vector_fallback_score !== null) {
    debugItems.push({
      icon: <Target className="w-4 h-4 text-pink-500" />,
      label: 'Vector Fallback Score',
      value: debugLog.vector_fallback_score.toFixed(5),
      type: 'metric' as const
    });
  }

  const renderValue = (item: typeof debugItems[0]) => {
    switch (item.type) {
      case 'boolean':
        return (
          <Badge variant={item.value ? 'default' : 'destructive'} className="text-xs">
            {item.value ? 'Yes' : 'No'}
          </Badge>
        );
      case 'badge':
        return (
          <Badge variant={item.badgeVariant} className="text-xs">
            {item.value}
          </Badge>
        );
      case 'metric':
        return (
          <span className="font-mono text-sm font-medium text-teal-600">
            {item.value}
          </span>
        );
      case 'text':
        return (
          <p className="text-sm text-gray-700 break-words">
            {item.value}
          </p>
        );
    }
  };

  return (
    <div className="space-y-3">
      {debugItems.map((item, index) => (
        <Card key={index} className="border-l-4 border-l-teal-500">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs font-medium text-gray-600 flex items-center gap-2">
              {item.icon}
              {item.label}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {renderValue(item)}
          </CardContent>
        </Card>
      ))}

      {/* Performance Summary */}
      <Card className="bg-teal-50 border-teal-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-teal-800">
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-600">Status:</span>
              <Badge
                variant={debugLog.tool_success ? 'default' : 'destructive'}
                className="ml-1 text-xs"
              >
                {debugLog.tool_success ? 'Success' : 'Failed'}
              </Badge>
            </div>
            <div>
              <span className="text-gray-600">Speed:</span>
              <Badge
                variant={debugLog.latency < 2 ? 'default' : debugLog.latency < 5 ? 'secondary' : 'destructive'}
                className="ml-1 text-xs"
              >
                {debugLog.latency < 2 ? 'Fast' : debugLog.latency < 5 ? 'Normal' : 'Slow'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugSidebar;

