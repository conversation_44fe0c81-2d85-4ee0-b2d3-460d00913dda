# Makefile for Conversational Sales AI Agent

# Install runtime dependencies
install:
	pip install -r requirements.txt

# Install full dev + linting + testing stack
dev-install:
	pip install -r dev-requirements.txt

# Run the FastAPI app locally
run:
	uvicorn main:app --reload

# Run tests
test:
	pytest tests/

# Run linting checks
lint:
	flake8 . && black --check . && isort --check-only .

# Format code automatically
format:
	black . && isort .

# Run Docker Compose
docker-up:
	docker-compose up --build

# Stop Docker Compose
docker-down:
	docker-compose down
