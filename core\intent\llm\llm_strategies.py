"""
LLM strategy patterns for reducing cyclomatic complexity.

This module contains strategy classes for different LLM operations:
- Model selection and fallback logic
- Response parsing strategies
- Retry mechanisms
"""

import os
import time
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from .llm_models import LLMIntentResult

logger = logging.getLogger(__name__)


class LLMModelStrategy:
    """Strategy for handling LLM model selection and fallback logic."""

    def __init__(self, client, primary_model: str, fallback_model: str):
        """
        Initialize model strategy.

        Args:
            client: LLM client instance
            primary_model: Primary model name
            fallback_model: Fallback model name
        """
        self.client = client
        self.primary_model = primary_model
        self.fallback_model = fallback_model

    def execute_with_fallback(
        self, prompt: str, tenant_id: Optional[str] = None
    ) -> Optional[LLMIntentResult]:
        """
        Execute LLM call with automatic fallback logic.

        Args:
            prompt: Prompt string
            tenant_id: Tenant identifier for context

        Returns:
            LLMIntentResult or None if both models fail
        """
        start_time = time.perf_counter()

        # Try primary model first
        result = self._try_primary_model(prompt)
        if result:
            processing_time = (time.perf_counter() - start_time) * 1000
            result.processing_time_ms = processing_time
            # Performance metrics enhancement integration - FIXED
            result.model_used = "gpt4o"
            self.client.update_stats(processing_time, result.tokens_used, "gpt4o")
            logger.debug(
                f"LLM intent parsed: {result.intent} (confidence: {result.confidence})"
            )
            return result

        # Fallback to secondary model
        logger.warning("GPT-4o failed after retries, trying GPT-3.5 fallback")
        result = self._try_fallback_model(prompt)
        if result:
            processing_time = (time.perf_counter() - start_time) * 1000
            result.processing_time_ms = processing_time
            # Performance metrics enhancement integration - FIXED
            result.model_used = "gpt35"
            self.client.update_stats(processing_time, result.tokens_used, "gpt35")
            logger.debug(
                f"LLM fallback intent parsed: {result.intent} (confidence: {result.confidence})"
            )
            return result

        # Both models failed
        logger.error("Both GPT-4o and GPT-3.5 failed")
        return None

    def _try_primary_model(self, prompt: str) -> Optional[LLMIntentResult]:
        """Try primary model with retry logic."""
        return self.client.call_llm_with_retry(
            prompt,
            os.environ.get("AZURE_DEPLOYMENT_GPT4O", self.primary_model),
            "gpt4o",
        )

    def _try_fallback_model(self, prompt: str) -> Optional[LLMIntentResult]:
        """Try fallback model without retry."""
        return self.client.call_llm(
            prompt,
            os.environ.get("AZURE_DEPLOYMENT_GPT35", self.fallback_model),
            "gpt35",
        )


class LLMPromptStrategy(ABC):
    """Abstract base class for prompt building strategies."""

    @abstractmethod
    def build_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Build prompt for the specific strategy."""
        pass

    @abstractmethod
    def validate_prompt(self, prompt: str) -> bool:
        """Validate prompt length and content."""
        pass


class BasicPromptStrategy(LLMPromptStrategy):
    """Basic prompt building strategy."""

    def __init__(self, prompt_builder):
        self.prompt_builder = prompt_builder

    def build_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Build basic prompt."""
        return self.prompt_builder.build_prompt(message, tenant_id, context)

    def validate_prompt(self, prompt: str) -> bool:
        """Validate basic prompt."""
        return len(prompt) < 4000  # Basic length check


class EnhancedPromptStrategy(LLMPromptStrategy):
    """Enhanced prompt building strategy with examples."""

    def __init__(self, prompt_builder):
        self.prompt_builder = prompt_builder

    def build_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Build enhanced prompt with examples."""
        return self.prompt_builder.build_enhanced_prompt(
            message, tenant_id, context, include_examples=True
        )

    def validate_prompt(self, prompt: str) -> bool:
        """Validate enhanced prompt length."""
        return self.prompt_builder.validate_prompt_length(prompt)


class LLMExecutionStrategy:
    """Strategy for executing LLM calls with different prompt strategies."""

    def __init__(self, model_strategy: LLMModelStrategy):
        self.model_strategy = model_strategy
        self.basic_strategy = None
        self.enhanced_strategy = None

    def set_prompt_strategies(
        self,
        basic_strategy: BasicPromptStrategy,
        enhanced_strategy: EnhancedPromptStrategy,
    ):
        """Set prompt strategies."""
        self.basic_strategy = basic_strategy
        self.enhanced_strategy = enhanced_strategy

    def execute_basic(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> Optional[LLMIntentResult]:
        """Execute with basic prompt strategy."""
        if not self.basic_strategy:
            raise ValueError("Basic prompt strategy not set")

        prompt = self.basic_strategy.build_prompt(message, tenant_id, context)
        if not self.basic_strategy.validate_prompt(prompt):
            logger.warning("Basic prompt validation failed")
            return None

        return self.model_strategy.execute_with_fallback(prompt, tenant_id)

    def execute_enhanced(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> Optional[LLMIntentResult]:
        """Execute with enhanced prompt strategy, fallback to basic if needed."""
        if not self.enhanced_strategy or not self.basic_strategy:
            raise ValueError("Prompt strategies not set")

        # Try enhanced prompt first
        enhanced_prompt = self.enhanced_strategy.build_prompt(
            message, tenant_id, context
        )
        if self.enhanced_strategy.validate_prompt(enhanced_prompt):
            result = self.model_strategy.execute_with_fallback(
                enhanced_prompt, tenant_id
            )
            if result:
                return result
            logger.warning("Enhanced prompt failed, trying basic prompt")
        else:
            logger.warning("Enhanced prompt too long, falling back to basic prompt")

        # Fallback to basic prompt
        return self.execute_basic(message, tenant_id, context)
