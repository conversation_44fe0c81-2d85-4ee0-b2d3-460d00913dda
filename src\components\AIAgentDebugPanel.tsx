import React, { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Brain, MessageSquare, RotateCcw, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import IntentParserPanel from '@/components/IntentParserPanel';
import SalesAgentPanel from '@/components/SalesAgentPanel';
import ConnectionStatusIndicator from '@/components/ConnectionStatusIndicator';
import { useTestSession } from '@/context/TestSessionContext';
import { getSessionDuration, getMessageStats, downloadSessionLogs } from '@/utils/sessionUtils';

const AIAgentDebugPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('intent-parser');
  const { intentSession, salesSession, resetAllSessions } = useTestSession();
  const { toast } = useToast();

  const intentStats = getMessageStats(intentSession);
  const salesStats = getMessageStats(salesSession);

  const handleDownloadLogs = () => {
    try {
      downloadSessionLogs(intentSession, salesSession);
      toast({
        title: "Download Started",
        description: "Session logs are being downloaded",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download session logs",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Agent Debug Panel</h1>
              <p className="text-sm text-gray-600 mt-1">
                Professional testing interface for Briskk AI agents
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <ConnectionStatusIndicator />

              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadLogs}
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Download Logs
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={resetAllSessions}
                className="flex items-center gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                Reset All
              </Button>
            </div>
          </div>

          {/* Session Stats */}
          <div className="flex items-center gap-6 mt-4">
            <div className="flex items-center gap-2">
              <Brain className="w-4 h-4 text-teal-600" />
              <span className="text-sm text-gray-600">Intent Parser:</span>
              <Badge variant="outline" className="text-xs">
                {intentStats.user} messages
              </Badge>
              <Badge variant="outline" className="text-xs">
                {getSessionDuration(intentSession)}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4 text-teal-600" />
              <span className="text-sm text-gray-600">Sales Agent:</span>
              <Badge variant="outline" className="text-xs">
                {salesStats.user} messages
              </Badge>
              <Badge variant="outline" className="text-xs">
                {getSessionDuration(salesSession)}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 bg-gray-50">
            <TabsList className="grid w-full max-w-md grid-cols-2 mx-6 my-4">
              <TabsTrigger 
                value="intent-parser" 
                className="flex items-center gap-2"
              >
                <Brain className="w-4 h-4" />
                Intent Parser Test
              </TabsTrigger>
              <TabsTrigger 
                value="sales-agent" 
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                Sales Agent Chat
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden">
            <TabsContent value="intent-parser" className="h-full m-0">
              <IntentParserPanel />
            </TabsContent>
            
            <TabsContent value="sales-agent" className="h-full m-0">
              <SalesAgentPanel />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 bg-gray-50 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>API Base: http://localhost:8000</span>
            {/* TODO: this will be picked from UI store context (React context) once we save it in state after tenant signs in  */}
            <span>Tenant ID: 0191fc1a-ae52-7796-9d29-5691aba7f284</span>
          </div>
          
          <div className="flex items-center gap-2">
            <span>Debug Interface</span>
            <Badge variant="outline" className="text-xs">
              v1.3.0
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAgentDebugPanel;
