"""
Primary fallback handler for intent parsing orchestration.

This module handles the main fallback scenarios when primary
parsing methods fail.
"""

import time
import logging
from typing import Dict, Optional, Any, List

from ...intent_config import FALL<PERSON>CK_INTENT
from ...intent_utils import (
    log_parsing_result,
    calculate_processing_time,
)
from ...response import ParseResponseBuilder
from ...intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class FallbackHandler:
    """Handles fallback scenarios when primary parsing methods fail."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def handle_fallback(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle fallback when no other method succeeds.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking
            user_id: User identifier for session state tracking

        Returns:
            Fallback response
        """
        source_chain.append("fallback")
        metadata.add_source("fallback")

        # Create fallback result
        fallback_result = response_builder.build_fallback_response(
            FALLBACK_INTENT, source_chain, message
        )

        # Try clarifier for fallback if enabled
        if self.orchestrator.enable_clarifier and self.orchestrator.clarifier:
            return self._handle_fallback_with_clarifier(
                fallback_result, session_id, tenant_id, metadata, 
                response_builder, message, user_id
            )
        else:
            # Clarifier disabled - return fallback result
            self.orchestrator.query_cache.set(message, fallback_result, tenant_id)
            log_parsing_result("fallback", FALLBACK_INTENT, 0.0)
            return fallback_result

    def _handle_fallback_with_clarifier(
        self,
        fallback_result: Dict[str, Any],
        session_id: Optional[str],
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        message: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Handle fallback with clarifier enabled."""
        clarifier_start = time.perf_counter()
        metadata.clarifier_triggered = True

        try:
            # Increment clarifier rounds in session state
            self.orchestrator._increment_clarifier_rounds(tenant_id, user_id)

            clarification_result = self.orchestrator.clarifier.get_clarification(
                fallback_result, session_id, tenant_id
            )

            metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)

            # Update response with clarifier metadata
            final_result = response_builder.update_with_clarifier_metadata(
                fallback_result, clarification_result
            )

            # Don't cache clarification prompts
            if not clarification_result.clarification_needed:
                self.orchestrator.query_cache.set(message, final_result, tenant_id)

            log_parsing_result("fallback", FALLBACK_INTENT, 0.0)
            return final_result

        except Exception as e:
            metadata.add_error(f"Clarifier flow failed: {str(e)}")
            metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)
            logger.error(f"Fallback clarifier failed: {e}")
            return fallback_result

    def validate_fallback_config(self) -> Dict[str, Any]:
        """
        Validate fallback configuration.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config": {}
        }

        # Check fallback intent configuration
        validation["config"]["fallback_intent"] = FALLBACK_INTENT
        
        # Check clarifier availability
        clarifier_available = (
            self.orchestrator.enable_clarifier and 
            self.orchestrator.clarifier is not None
        )
        validation["config"]["clarifier_available"] = clarifier_available
        
        if self.orchestrator.enable_clarifier and not self.orchestrator.clarifier:
            validation["warnings"].append("Clarifier enabled but not available")

        # Check cache availability for fallback caching
        cache_available = self.orchestrator.query_cache is not None
        validation["config"]["cache_available"] = cache_available
        
        if not cache_available:
            validation["warnings"].append("Cache not available for fallback caching")

        return validation

    def get_fallback_stats(self) -> Dict[str, Any]:
        """
        Get fallback usage statistics.
        
        Returns:
            Dictionary with fallback statistics
        """
        # This would typically come from performance tracker
        # For now, return basic info
        return {
            "fallback_handler_available": True,
            "clarifier_fallback_enabled": (
                self.orchestrator.enable_clarifier and 
                self.orchestrator.clarifier is not None
            ),
            "cache_fallback_enabled": self.orchestrator.query_cache is not None,
        }
