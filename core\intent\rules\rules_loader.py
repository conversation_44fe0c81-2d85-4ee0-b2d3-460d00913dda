"""
Rules loader for intent detection system.

This module handles loading and validation of YAML rule files for both base
and tenant-specific intent detection rules.

TODO: Modularize this 385-line file into focused modules: rule_loader_core.py, rule_conflict_detector.py, rule_validator.py - Robustness Improvement
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from .rule_models import IntentRule, RuleConflict, RuleValidationResult
from .rule_engine import RuleEngine

logger = logging.getLogger(__name__)


class RulesLoader:
    """
    Loads and manages intent rules from YAML configuration files.

    Features:
    - Load base rules and tenant-specific overrides
    - Hot reloading of rule configurations
    - Rule validation and error handling
    - Multi-language support
    - Rule merging and priority management
    """

    def __init__(self, config_dir: str = "config"):
        """
        Initialize the rules loader.

        Args:
            config_dir: Directory containing rule configuration files
        """
        self.config_dir = Path(config_dir)
        self.base_rules_file = self.config_dir / "base_rules.yaml"
        self.loaded_rules: Dict[str, Dict[str, Any]] = {}
        self._file_timestamps: Dict[str, float] = {}
        self.base_rule_ids: set = set()  # Track base rule IDs for conflict detection

    def load_rules(
        self, rule_engine: RuleEngine, tenant_id: Optional[str] = None
    ) -> int:
        """
        Load rules into the rule engine.

        Args:
            rule_engine: RuleEngine instance to load rules into
            tenant_id: Optional tenant ID for tenant-specific rules

        Returns:
            Number of rules loaded
        """
        rules_loaded = 0

        try:
            if tenant_id:
                # Load tenant-specific rules
                tenant_file = self.config_dir / f"rules_{tenant_id}.yaml"
                if tenant_file.exists():
                    rules_data = self._load_yaml_file(tenant_file)
                    if rules_data:
                        tenant_rules = self._parse_rules(rules_data, tenant_id)
                        for rule in tenant_rules:
                            if rule_engine.add_rule(rule):
                                rules_loaded += 1
                        logger.info(
                            f"Loaded {rules_loaded} rules for tenant {tenant_id}"
                        )
                else:
                    logger.warning(f"Tenant rules file not found: {tenant_file}")
            else:
                # Load base rules
                if self.base_rules_file.exists():
                    rules_data = self._load_yaml_file(self.base_rules_file)
                    if rules_data:
                        base_rules = self._parse_rules(rules_data)
                        for rule in base_rules:
                            if rule_engine.add_rule(rule):
                                rules_loaded += 1
                                self.base_rule_ids.add(rule.id)
                        logger.info(f"Loaded {rules_loaded} base rules")
                else:
                    logger.error(f"Base rules file not found: {self.base_rules_file}")

        except Exception as e:
            logger.error(f"Failed to load rules: {e}")

        return rules_loaded

    def _load_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Load and parse YAML file.

        Args:
            file_path: Path to YAML file

        Returns:
            Parsed YAML data or None if failed
        """
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                data = yaml.safe_load(file)

            # Update file timestamp for change detection
            self._file_timestamps[str(file_path)] = file_path.stat().st_mtime

            return data

        except Exception as e:
            logger.error(f"Failed to load YAML file {file_path}: {e}")
            return None

    def _parse_rules(
        self, rules_data: Dict[str, Any], tenant_id: Optional[str] = None
    ) -> List[IntentRule]:
        """
        Parse rules from YAML data.

        Args:
            rules_data: Parsed YAML data
            tenant_id: Optional tenant ID

        Returns:
            List of IntentRule objects
        """
        rules = []

        try:
            rules_list = rules_data.get("rules", [])

            for rule_data in rules_list:
                rule = self._create_rule_from_data(rule_data, tenant_id)
                if rule:
                    rules.append(rule)

        except Exception as e:
            logger.error(f"Failed to parse rules: {e}")

        return rules

    def _create_rule_from_data(
        self, rule_data: Dict[str, Any], tenant_id: Optional[str] = None
    ) -> Optional[IntentRule]:
        """
        Create IntentRule from rule data.

        Args:
            rule_data: Rule data dictionary
            tenant_id: Optional tenant ID

        Returns:
            IntentRule object or None if invalid
        """
        try:
            # Validate required fields
            required_fields = ["id", "intent", "patterns"]
            for field in required_fields:
                if field not in rule_data:
                    logger.error(f"Missing required field '{field}' in rule")
                    return None

            # Create rule
            rule = IntentRule(
                id=rule_data["id"],
                intent=rule_data["intent"],
                patterns=rule_data["patterns"],
                entities=rule_data.get("entities", {}),
                locale=rule_data.get("locale", ["en"]),
                priority=rule_data.get("priority", 100),
                tenant_id=tenant_id,
                source="tenant" if tenant_id else "base",
            )

            return rule

        except Exception as e:
            logger.error(f"Failed to create rule from data: {e}")
            return None

    def get_available_tenants(self) -> List[str]:
        """
        Get list of available tenant rule files.

        Returns:
            List of tenant IDs that have rule files
        """
        tenants = []

        try:
            for file_path in self.config_dir.glob("rules_*.yaml"):
                # Extract tenant ID from filename
                filename = file_path.stem
                if filename.startswith("rules_"):
                    tenant_id = filename[6:]  # Remove "rules_" prefix
                    tenants.append(tenant_id)

        except Exception as e:
            logger.error(f"Failed to get available tenants: {e}")

        return tenants

    def check_for_updates(self, rule_engine: RuleEngine) -> bool:
        """
        Check for rule file updates and reload if necessary.

        Args:
            rule_engine: RuleEngine instance to update

        Returns:
            True if rules were reloaded
        """
        try:
            files_to_check = [self.base_rules_file]

            # Add tenant rule files
            for tenant_id in self.get_available_tenants():
                tenant_file = self.config_dir / f"rules_{tenant_id}.yaml"
                files_to_check.append(tenant_file)

            # Check for file changes
            files_changed = []
            for file_path in files_to_check:
                if file_path.exists():
                    current_mtime = file_path.stat().st_mtime
                    stored_mtime = self._file_timestamps.get(str(file_path), 0)

                    if current_mtime > stored_mtime:
                        files_changed.append(file_path)

            if files_changed:
                logger.info(f"Rule files changed: {[str(f) for f in files_changed]}")

                # Clear existing rules
                rule_engine.clear_rules()

                # Reload base rules
                self.load_rules(rule_engine)

                # Reload tenant rules
                for tenant_id in self.get_available_tenants():
                    self.load_rules(rule_engine, tenant_id)

                return True

        except Exception as e:
            logger.error(f"Failed to check for rule updates: {e}")

        return False

    def validate_rules(self, rule_engine: RuleEngine) -> RuleValidationResult:
        """
        Validate loaded rules for conflicts and issues.

        Args:
            rule_engine: RuleEngine instance to validate

        Returns:
            RuleValidationResult with validation details
        """
        result = RuleValidationResult(
            valid=True,
            errors=[],
            warnings=[],
            conflicts=[],
            rule_count=len(rule_engine.get_base_rules()),
            tenant_rule_count=sum(
                len(rules) for rules in rule_engine.tenant_rules.values()
            ),
        )

        try:
            # Check for rule conflicts
            conflicts = self._detect_rule_conflicts(rule_engine)
            result.conflicts.extend(conflicts)

            # Add warnings for conflicts
            for conflict in conflicts:
                if conflict.severity == "error":
                    result.errors.append(conflict.description)
                    result.valid = False
                else:
                    result.warnings.append(conflict.description)

        except Exception as e:
            result.errors.append(f"Validation failed: {e}")
            result.valid = False

        return result

    def _detect_rule_conflicts(self, rule_engine: RuleEngine) -> List[RuleConflict]:
        """
        Detect conflicts between rules.

        Args:
            rule_engine: RuleEngine instance

        Returns:
            List of detected conflicts
        """
        conflicts = []

        try:
            # Check tenant rule conflicts with base rules
            for tenant_id, tenant_rules in rule_engine.tenant_rules.items():
                for rule_id, tenant_rule in tenant_rules.items():
                    # Check if tenant rule overrides base rule
                    if rule_id in self.base_rule_ids:
                        base_rule = rule_engine.get_base_rules().get(rule_id)
                        if base_rule and base_rule.intent != tenant_rule.intent:
                            # Warning logs when tenant rules silently override base rules - Production Blocker FIXED
                            logger.warning(f"Tenant rule override detected: {tenant_id}:{rule_id} intent '{tenant_rule.intent}' overrides base rule intent '{base_rule.intent}'")
                            conflicts.append(
                                RuleConflict(
                                    rule_id=rule_id,
                                    conflicting_rule_id=f"base:{rule_id}",
                                    conflict_type="intent_mismatch",
                                    description=f"Tenant rule {tenant_id}:{rule_id} intent '{tenant_rule.intent}' conflicts with base rule intent '{base_rule.intent}'",
                                    severity="warning",
                                )
                            )

        except Exception as e:
            logger.error(f"Failed to detect rule conflicts: {e}")

        return conflicts

    def export_rules(self, rule_engine: RuleEngine, output_file: Path) -> bool:
        """
        Export loaded rules to YAML file.

        Args:
            rule_engine: RuleEngine instance
            output_file: Output file path

        Returns:
            True if export successful
        """
        try:
            export_data = {
                "rules": [],
                "metadata": {
                    "base_rule_count": len(rule_engine.get_base_rules()),
                    "tenant_rule_count": sum(
                        len(rules) for rules in rule_engine.tenant_rules.values()
                    ),
                },
            }

            # Export base rules
            for rule in rule_engine.get_base_rules().values():
                export_data["rules"].append(self._rule_to_dict(rule))

            # Export tenant rules
            for tenant_id, tenant_rules in rule_engine.tenant_rules.items():
                for rule in tenant_rules.values():
                    export_data["rules"].append(self._rule_to_dict(rule))

            # Write to file
            with open(output_file, "w", encoding="utf-8") as file:
                yaml.dump(
                    export_data, file, default_flow_style=False, allow_unicode=True
                )

            logger.info(f"Rules exported to {output_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to export rules: {e}")
            return False

    def _rule_to_dict(self, rule: IntentRule) -> Dict[str, Any]:
        """Convert IntentRule to dictionary for export."""
        return {
            "id": rule.id,
            "intent": rule.intent,
            "patterns": rule.patterns,
            "entities": rule.entities,
            "locale": rule.locale,
            "priority": rule.priority,
            "tenant_id": rule.tenant_id,
            "source": rule.source,
        }
