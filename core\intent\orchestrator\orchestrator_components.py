"""
Component initialization for intent parsing orchestration.

This module handles the initialization and setup of all core components
used by the orchestrator.
"""

import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass

from ..rules import RuleEngine, RulesLoader
from ..intent_cache import QueryCache
from ..llm import LLMIntentParser
from ..clarifier import IntentClarifier
from ..performance_tracker import get_performance_tracker

# Session state integration
from core.session.session_state import SessionStore
from clients.redis_client import RedisClient

logger = logging.getLogger(__name__)


@dataclass
class OrchestrationComponents:
    """Container for all orchestration components."""
    
    # Core components
    rule_engine: RuleEngine
    rules_loader: RulesLoader
    query_cache: QueryCache
    llm_parser: Optional[LLMIntentParser]
    clarifier: Optional[IntentClarifier]
    session_store: Optional[SessionStore]
    performance_tracker: any
    
    # Configuration
    config_dir: str
    enable_llm: bool
    enable_clarifier: bool

    @classmethod
    def create(
        cls,
        config_dir: str = "config",
        enable_llm: bool = True,
        enable_clarifier: bool = True,
        session_store: Optional[SessionStore] = None,
    ) -> "OrchestrationComponents":
        """
        Create and initialize all orchestration components.
        
        Args:
            config_dir: Directory containing rule configuration files
            enable_llm: Whether to enable LLM parsing
            enable_clarifier: Whether to enable clarification system
            session_store: Optional SessionStore instance
            
        Returns:
            Initialized OrchestrationComponents instance
        """
        logger.info("Initializing orchestration components...")
        
        # Initialize core components
        rule_engine = RuleEngine()
        rules_loader = RulesLoader(config_dir)
        query_cache = QueryCache()
        
        # Initialize optional components
        llm_parser = LLMIntentParser() if enable_llm else None
        clarifier = IntentClarifier() if enable_clarifier else None
        
        # Initialize session store
        if not session_store:
            session_store = cls._create_default_session_store(clarifier)
        
        # Initialize performance tracker
        performance_tracker = get_performance_tracker()
        
        components = cls(
            rule_engine=rule_engine,
            rules_loader=rules_loader,
            query_cache=query_cache,
            llm_parser=llm_parser,
            clarifier=clarifier,
            session_store=session_store,
            performance_tracker=performance_tracker,
            config_dir=config_dir,
            enable_llm=enable_llm,
            enable_clarifier=enable_clarifier,
        )
        
        # Load rules
        cls._load_initial_rules(components)
        
        logger.info("Orchestration components initialized successfully")
        return components

    @staticmethod
    def _create_default_session_store(clarifier: Optional[IntentClarifier]) -> SessionStore:
        """Create default SessionStore using the same Redis client as clarifier."""
        try:
            # Use the same Redis client as clarifier if available
            if clarifier and hasattr(clarifier, 'redis_client'):
                redis_client = clarifier.redis_client
            else:
                # Create new Redis client with default settings
                import config.settings as settings
                redis_client = RedisClient(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB
                )
            return SessionStore(redis_client)
        except Exception as e:
            logger.warning(f"Failed to create SessionStore: {e}")
            # Return a session store with None redis client
            return SessionStore(None)

    @staticmethod
    def _load_initial_rules(components: "OrchestrationComponents") -> None:
        """Load initial rules into the rule engine."""
        try:
            # Load base rules
            base_rules_count = components.rules_loader.load_rules(components.rule_engine)

            # Load tenant-specific rules
            available_tenants = components.rules_loader.get_available_tenants()
            tenant_rules_count = 0
            for tenant_id in available_tenants:
                tenant_count = components.rules_loader.load_rules(components.rule_engine, tenant_id)
                tenant_rules_count += tenant_count

            total_rules = base_rules_count + tenant_rules_count
            logger.info(f"Loaded {total_rules} total rules ({base_rules_count} base, {tenant_rules_count} tenant)")

        except Exception as e:
            logger.error(f"Failed to load initial rules: {e}")
            raise

    def validate_components(self) -> Dict[str, Any]:
        """
        Validate all components are properly initialized.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "component_status": {}
        }
        
        # Check core components
        components_to_check = [
            ("rule_engine", self.rule_engine),
            ("rules_loader", self.rules_loader),
            ("query_cache", self.query_cache),
            ("performance_tracker", self.performance_tracker),
        ]
        
        for name, component in components_to_check:
            if component is None:
                validation["errors"].append(f"{name} is not initialized")
                validation["valid"] = False
            validation["component_status"][name] = component is not None
        
        # Check optional components
        if self.enable_llm and self.llm_parser is None:
            validation["errors"].append("LLM enabled but parser not initialized")
            validation["valid"] = False
        
        if self.enable_clarifier and self.clarifier is None:
            validation["errors"].append("Clarifier enabled but not initialized")
            validation["valid"] = False
        
        validation["component_status"]["llm_parser"] = self.llm_parser is not None
        validation["component_status"]["clarifier"] = self.clarifier is not None
        validation["component_status"]["session_store"] = self.session_store is not None
        
        # Check rule loading
        rule_count = self.rule_engine.get_rule_count()
        if rule_count == 0:
            validation["warnings"].append("No rules loaded")
        
        validation["component_status"]["rules_loaded"] = rule_count
        
        return validation

    def cleanup(self) -> None:
        """Clean up component resources."""
        try:
            # Clear performance stats
            if self.performance_tracker:
                self.performance_tracker.clear_stats()
            
            # Clear cache
            if self.query_cache:
                self.query_cache.clear_cache()
            
            logger.info("Component cleanup completed")
            
        except Exception as e:
            logger.error(f"Component cleanup failed: {e}")
