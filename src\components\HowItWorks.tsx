
import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";

const steps = [
  {
    number: "01",
    title: "Seamless Integration",
    description: "Connect to your existing e-commerce platform, CRM, and inventory systems through our API or pre-built integrations. No disruption to your operations.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
        <path d="M20 7h-9"></path>
        <path d="M14 17H5"></path>
        <circle cx="17" cy="17" r="3"></circle>
        <circle cx="7" cy="7" r="3"></circle>
      </svg>
    )
  },
  {
    number: "02",
    title: "Knowledge Transfer",
    description: "Our AI ingests your product catalog, brand guidelines, and customer service protocols to embody your unique brand voice and expertise.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
        <path d="M9 3H5a2 2 0 0 0-2 2v4"></path>
        <path d="M5 21h4"></path>
        <path d="M19 3h-4"></path>
        <path d="M19 21h-4"></path>
        <path d="M3 9v6"></path>
        <path d="M21 9v6"></path>
        <path d="M9 3v18"></path>
        <path d="M15 3v18"></path>
      </svg>
    )
  },
  {
    number: "03",
    title: "Channel Deployment",
    description: "Deploy your AI retail agent across your website, mobile app, in-store displays, social media, and messaging platforms with a few clicks.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
        <path d="M21 2H3v16h5v4l4-4h5l4-4V2z"></path>
        <path d="M10 8h4"></path>
        <path d="M12 6v4"></path>
      </svg>
    )
  },
  {
    number: "04",
    title: "Continuous Learning",
    description: "Your AI agent gets smarter with every interaction, learning from customer conversations, purchase patterns, and your feedback to constantly improve.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
        <path d="M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z"></path>
        <path d="M10 2c1 .5 2 2 2 5"></path>
      </svg>
    )
  }
];

const HowItWorks = () => {
  return (
    <section id="how-it-works" className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container px-4 mx-auto max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="max-w-lg"
          >
            <div className="inline-block px-3 py-1 text-xs font-semibold bg-briskk-50 text-briskk-600 rounded-full mb-4">
              Implementation
            </div>
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">
              Launch in weeks, <span className="bg-gradient-to-r from-briskk-700 to-briskk-500 bg-clip-text text-transparent">not months</span>
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Our streamlined implementation process gets your AI retail agents working quickly with measurable ROI, 
              minimal IT overhead, and continuous improvement.
            </p>
            
            <div className="space-y-6">
              {steps.map((step, index) => (
                <motion.div 
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex items-start gap-4"
                >
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-briskk-600 flex items-center justify-center">
                    {step.icon}
                  </div>
                  <div>
                    <div className="flex items-center mb-1">
                      <span className="text-sm font-bold text-briskk-600 mr-2">{step.number}</span>
                      <h3 className="text-lg font-semibold">{step.title}</h3>
                    </div>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
            
            <div className="mt-10">
              <Button className="px-6 py-5 text-base font-medium bg-briskk-600 hover:bg-briskk-700 text-white rounded-full shadow-lg shadow-briskk-100/30">
                Schedule Implementation Call
              </Button>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img 
                src="https://images.unsplash.com/photo-1590650153855-d9e808231d41?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                alt="Retail Implementation" 
                className="w-full h-auto object-cover rounded-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="p-8 text-white">
                  <h3 className="text-2xl font-semibold mb-2">Launch Timeline</h3>
                  <p className="mb-4">From contract to deployment in as little as 2 weeks</p>
                  <div className="w-full bg-white/20 h-2 rounded-full overflow-hidden">
                    <div className="bg-briskk-500 h-full w-3/4 rounded-full"></div>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span>Contract</span>
                    <span>Integration</span>
                    <span>Training</span>
                    <span>Live</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="absolute -bottom-6 -left-6 -z-10 w-64 h-64 bg-gradient-to-tr from-briskk-100 to-briskk-50 rounded-full blur-3xl opacity-60"></div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
