# 🧠 Briskk.ai – AI Copilot for D2C Brands

This is the official marketing and product site for **Briskk.ai**, an AI-powered commerce copilot that helps D2C brands automate sales, engagement, and growth via WhatsApp and beyond.

---

## 🔗 Live Preview

**URL**: https://www.briskk.ai  
**Staging / Netlify Preview**: https://app.netlify.com/projects/briskkai/overview
<NAME_EMAIL> GMAIL

---

## 🚀 Tech Stack

- **Framework**: Vite + React + TypeScript
- **Styling**: Tailwind CSS
- **UI Kit**: [shadcn/ui](https://ui.shadcn.dev)
- **Dev Platform**: [Lovable](https://lovable.dev)
- **Deploy Platform**: [Netlify](https://netlify.com)

---

## 🛠️ Local Development

> Prerequisites: [Node.js](https://nodejs.org/) + npm installed (we recommend using [nvm](https://github.com/nvm-sh/nvm))

```bash
# 1. Clone the repository
<NAME_EMAIL>:ChannelBlendTech/briskk-ai-website.git

# 2. Navigate into the project
cd briskk-ai-website

# 3. Install dependencies
npm install

# 4. Start the development server
npm run dev
```
