"""
Flow controller for intent parsing orchestration.

This module contains the main flow control logic that coordinates
the complete intent parsing pipeline.
"""

import logging
from typing import Dict, Optional, Any

from ..intent_config import get_default_setting
from ..intent_utils import load_tenant_metadata, log_tenant_metadata
from ..response import ParseResponseBuilder
from ..metadata_utils import create_parse_metadata

logger = logging.getLogger(__name__)


class OrchestrationFlowController:
    """Controls the main intent parsing flow execution."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def execute_parse_flow(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        locale: str = None,
    ) -> Dict[str, Any]:
        """
        Execute the complete intent parsing flow.
        
        Args:
            message: User input message
            tenant_id: Tenant identifier
            session_id: Session identifier
            user_id: User identifier
            locale: Language locale
            
        Returns:
            Dictionary with intent parsing results
        """
        # Use default locale if not specified
        locale = locale or get_default_setting("locale")
        
        # Use session_id as user_id fallback for backward compatibility
        effective_user_id = user_id or session_id
        
        # Initialize source chain tracking for observability
        source_chain = []
        
        # Create metadata for tracking
        metadata = create_parse_metadata(message, tenant_id, session_id, locale)
        
        # Check clarifier rounds and handle escalation
        escalation_result = self._check_escalation_needed(
            tenant_id, effective_user_id, session_id, message, source_chain
        )
        if escalation_result:
            return self.orchestrator.managers.finalize_result(escalation_result, metadata)
        
        try:
            # Load tenant metadata and create response builder
            response_builder = self._setup_tenant_context(
                metadata, tenant_id, session_id
            )
            
            # Execute the parsing pipeline
            result = self._execute_parsing_pipeline(
                message, tenant_id, session_id, locale, metadata,
                response_builder, source_chain, effective_user_id
            )
            
            return self.orchestrator.managers.finalize_result(result, metadata)
            
        except Exception as e:
            return self._handle_flow_error(
                e, message, tenant_id, session_id, metadata, source_chain
            )

    def _check_escalation_needed(
        self,
        tenant_id: Optional[str],
        user_id: Optional[str],
        session_id: Optional[str],
        message: str,
        source_chain: list,
    ) -> Optional[Dict[str, Any]]:
        """Check if escalation is needed due to max clarifier rounds."""
        # Get session context for clarifier round tracking
        session_context = self.orchestrator.managers.get_session_context(tenant_id, user_id)
        clarifier_rounds = session_context.get("clarifier_rounds", 0)
        
        # Check if clarifier has reached maximum rounds
        if self.orchestrator.managers.check_clarifier_max_rounds(clarifier_rounds):
            logger.info(f"Clarifier max rounds reached ({clarifier_rounds}), returning escalation intent")
            response_builder = ParseResponseBuilder(
                session_id=session_id, tenant_id=tenant_id
            )
            return response_builder.build_escalation_response(
                message, clarifier_rounds, source_chain
            )
        
        return None

    def _setup_tenant_context(
        self,
        metadata,
        tenant_id: Optional[str],
        session_id: Optional[str],
    ) -> ParseResponseBuilder:
        """Setup tenant context and create response builder."""
        # Load tenant metadata for traceability
        tenant_metadata, vertical, custom_intents_count = load_tenant_metadata(tenant_id)
        metadata.tenant_vertical = vertical
        metadata.custom_intents_count = custom_intents_count
        
        log_tenant_metadata(tenant_id, vertical, custom_intents_count)
        
        # Create response builder with tenant context
        return ParseResponseBuilder(
            session_id=session_id,
            tenant_id=tenant_id,
            vertical=vertical,
            custom_intents_count=custom_intents_count,
        )

    def _execute_parsing_pipeline(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        locale: str,
        metadata,
        response_builder: ParseResponseBuilder,
        source_chain: list,
        user_id: Optional[str],
    ) -> Dict[str, Any]:
        """Execute the main parsing pipeline steps."""
        # Get session context for LLM
        session_context = self.orchestrator.managers.get_session_context(tenant_id, user_id)
        
        # Step 1: Check cache first (fastest)
        result = self.orchestrator.managers.try_cache(
            message, tenant_id, metadata, response_builder, source_chain
        )
        if result:
            confidence = result.get("confidence", 0.0)
            if confidence >= 0.80:
                self.orchestrator.managers.reset_clarifier_rounds(tenant_id, user_id)
            return result
        
        # Step 2: Try Rule Engine (fast, deterministic)
        result = self.orchestrator.managers.try_rule_engine(
            message, tenant_id, locale, metadata, response_builder, source_chain
        )
        if result:
            confidence = result.get("confidence", 0.0)
            if confidence >= 0.80:
                self.orchestrator.managers.reset_clarifier_rounds(tenant_id, user_id)
            return result
        
        # Step 3: LLM Parser fallback
        result = self.orchestrator.managers.try_llm_parser(
            message, tenant_id, session_id, locale, metadata,
            response_builder, source_chain, user_id, session_context
        )
        if result:
            confidence = result.get("confidence", 0.0)
            if confidence >= 0.80:
                self.orchestrator.managers.reset_clarifier_rounds(tenant_id, user_id)
            return result
        
        # Step 4: Final fallback - no match found
        return self.orchestrator.managers.handle_fallback(
            message, tenant_id, session_id, metadata,
            response_builder, source_chain, user_id
        )

    def _handle_flow_error(
        self,
        error: Exception,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        metadata,
        source_chain: list,
    ) -> Dict[str, Any]:
        """Handle errors that occur during flow execution."""
        metadata.add_error(f"Orchestration failed: {str(error)}")
        logger.error(f"Parse orchestration failed: {error}")
        
        # Return error response
        response_builder = ParseResponseBuilder(
            session_id=session_id, tenant_id=tenant_id
        )
        error_result = response_builder.build_error_response(str(error), source_chain)
        return self.orchestrator.managers.finalize_result(error_result, metadata)
