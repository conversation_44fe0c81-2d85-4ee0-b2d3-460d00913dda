from schemas.message_schema import UserMessage, AgentResponse

class SalesAgentHandler:
    def __init__(self, flow_router):
        self.flow_router = flow_router

    def handle_chat(self, user_message: UserMessage) -> AgentResponse:
        if not self.flow_router:
            return AgentResponse(status="error", content="Flow router not configured", metadata={})

        try:
            response = self.flow_router.route(user_message)
            return AgentResponse(status="success", content=response, metadata={})
        except Exception as e:
            return AgentResponse(status="error", content=str(e), metadata={})
