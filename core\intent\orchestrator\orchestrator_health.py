"""
Health management for intent parsing orchestration.

This module handles health checks, validation, and diagnostic operations
for the orchestrator and its components.
"""

import logging
from typing import Dict, Optional, Any

logger = logging.getLogger(__name__)


class OrchestrationHealthManager:
    """Manages health checks and validation for orchestration components."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check on all components."""
        health_status = {
            "overall_status": "healthy",
            "components": {},
            "errors": [],
            "warnings": []
        }

        # Check rule engine
        try:
            rule_health = self._check_rule_engine_health()
            health_status["components"]["rule_engine"] = rule_health
            if not rule_health["healthy"]:
                health_status["overall_status"] = "degraded"
                health_status["errors"].extend(rule_health.get("errors", []))
        except Exception as e:
            health_status["components"]["rule_engine"] = {"healthy": False, "error": str(e)}
            health_status["overall_status"] = "unhealthy"
            health_status["errors"].append(f"Rule engine health check failed: {e}")

        # Check query cache
        try:
            cache_health = self._check_cache_health()
            health_status["components"]["query_cache"] = cache_health
            if not cache_health["healthy"]:
                health_status["overall_status"] = "degraded"
                health_status["warnings"].extend(cache_health.get("warnings", []))
        except Exception as e:
            health_status["components"]["query_cache"] = {"healthy": False, "error": str(e)}
            health_status["warnings"].append(f"Cache health check failed: {e}")

        # Check LLM parser
        if self.orchestrator.enable_llm and self.orchestrator.llm_parser:
            try:
                llm_health = self._check_llm_health()
                health_status["components"]["llm_parser"] = llm_health
                if not llm_health["healthy"]:
                    health_status["overall_status"] = "degraded"
                    health_status["warnings"].extend(llm_health.get("warnings", []))
            except Exception as e:
                health_status["components"]["llm_parser"] = {"healthy": False, "error": str(e)}
                health_status["warnings"].append(f"LLM health check failed: {e}")

        # Check clarifier
        if self.orchestrator.enable_clarifier and self.orchestrator.clarifier:
            try:
                clarifier_health = self._check_clarifier_health()
                health_status["components"]["clarifier"] = clarifier_health
                if not clarifier_health["healthy"]:
                    health_status["overall_status"] = "degraded"
                    health_status["warnings"].extend(clarifier_health.get("warnings", []))
            except Exception as e:
                health_status["components"]["clarifier"] = {"healthy": False, "error": str(e)}
                health_status["warnings"].append(f"Clarifier health check failed: {e}")

        # Check session store
        try:
            session_health = self._check_session_health()
            health_status["components"]["session_store"] = session_health
            if not session_health["healthy"]:
                health_status["warnings"].extend(session_health.get("warnings", []))
        except Exception as e:
            health_status["components"]["session_store"] = {"healthy": False, "error": str(e)}
            health_status["warnings"].append(f"Session store health check failed: {e}")

        return health_status

    def _check_rule_engine_health(self) -> Dict[str, Any]:
        """Check rule engine health."""
        health = {"healthy": True, "errors": [], "info": {}}

        try:
            # Check if rules are loaded
            rule_count = self.orchestrator.rule_engine.get_rule_count()
            health["info"]["rule_count"] = rule_count

            if rule_count == 0:
                health["healthy"] = False
                health["errors"].append("No rules loaded")

            # Validate rules
            validation = self.orchestrator.rule_engine.validate_rules()
            health["info"]["validation"] = validation

            if not validation["valid"]:
                health["healthy"] = False
                health["errors"].extend(validation["errors"])

        except Exception as e:
            health["healthy"] = False
            health["errors"].append(f"Rule engine check failed: {e}")

        return health

    def _check_cache_health(self) -> Dict[str, Any]:
        """Check query cache health."""
        health = {"healthy": True, "warnings": [], "info": {}}

        try:
            # Test cache connectivity
            test_key = "health_check_test"
            test_value = {"test": True}
            
            # Try to set and get a test value
            self.orchestrator.query_cache.set(test_key, test_value)
            retrieved = self.orchestrator.query_cache.get(test_key)
            
            if retrieved is None:
                health["healthy"] = False
                health["warnings"].append("Cache set/get test failed")
            else:
                # Clean up test key
                self.orchestrator.query_cache.clear_cache()

            # Get cache stats if available
            if hasattr(self.orchestrator.query_cache, 'get_stats'):
                health["info"]["stats"] = self.orchestrator.query_cache.get_stats()

        except Exception as e:
            health["healthy"] = False
            health["warnings"].append(f"Cache connectivity test failed: {e}")

        return health

    def _check_llm_health(self) -> Dict[str, Any]:
        """Check LLM parser health."""
        health = {"healthy": True, "warnings": [], "info": {}}

        try:
            # Validate LLM configuration
            validation = self.orchestrator.llm_parser.validate_configuration()
            health["info"]["validation"] = validation

            if not validation["valid"]:
                health["healthy"] = False
                health["warnings"].extend(validation["errors"])

            # Test model availability if validation passes
            if validation["valid"]:
                try:
                    model_tests = self.orchestrator.llm_parser.test_models()
                    health["info"]["model_tests"] = model_tests
                    
                    if not model_tests["primary_model"]["available"]:
                        health["warnings"].append("Primary model not available")
                    
                    if not model_tests["fallback_model"]["available"]:
                        health["warnings"].append("Fallback model not available")

                except Exception as e:
                    health["warnings"].append(f"Model availability test failed: {e}")

        except Exception as e:
            health["healthy"] = False
            health["warnings"].append(f"LLM health check failed: {e}")

        return health

    def _check_clarifier_health(self) -> Dict[str, Any]:
        """Check clarifier health."""
        health = {"healthy": True, "warnings": [], "info": {}}

        try:
            # Check if clarifier has required components
            if hasattr(self.orchestrator.clarifier, 'redis_client'):
                health["info"]["redis_available"] = True
            else:
                health["warnings"].append("Clarifier Redis client not available")

            # Test clarifier functionality with a mock result
            test_result = {
                "intent": "test",
                "confidence": 0.5,
                "entities": {},
                "source": "test"
            }
            
            # This should not fail for a healthy clarifier
            clarification = self.orchestrator.clarifier.get_clarification(
                test_result, "health_check_session", "test_tenant"
            )
            
            health["info"]["clarification_test"] = {
                "success": True,
                "clarification_needed": clarification.clarification_needed
            }

        except Exception as e:
            health["healthy"] = False
            health["warnings"].append(f"Clarifier functionality test failed: {e}")

        return health

    def _check_session_health(self) -> Dict[str, Any]:
        """Check session store health."""
        health = {"healthy": True, "warnings": [], "info": {}}

        try:
            if not self.orchestrator.session_store:
                health["healthy"] = False
                health["warnings"].append("Session store not initialized")
                return health

            # Test session operations
            test_tenant = "health_check"
            test_user = "test_user"
            test_context = {"test": True}

            # Try to save and retrieve context
            self.orchestrator.session_store.save_context(test_tenant, test_user, test_context)
            retrieved_context = self.orchestrator.session_store.get_context(test_tenant, test_user)

            if retrieved_context.get("test") != True:
                health["warnings"].append("Session context save/retrieve test failed")

            health["info"]["session_test"] = {"success": True}

        except Exception as e:
            health["healthy"] = False
            health["warnings"].append(f"Session store test failed: {e}")

        return health

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate orchestrator configuration."""
        validation = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "configuration": {}
        }

        try:
            # Check basic configuration
            validation["configuration"] = {
                "config_dir": self.orchestrator.config_dir,
                "enable_llm": self.orchestrator.enable_llm,
                "enable_clarifier": self.orchestrator.enable_clarifier,
                "components_initialized": {
                    "rule_engine": self.orchestrator.rule_engine is not None,
                    "rules_loader": self.orchestrator.rules_loader is not None,
                    "query_cache": self.orchestrator.query_cache is not None,
                    "llm_parser": self.orchestrator.llm_parser is not None,
                    "clarifier": self.orchestrator.clarifier is not None,
                    "session_store": self.orchestrator.session_store is not None,
                }
            }

            # Validate component configurations
            if self.orchestrator.enable_llm and not self.orchestrator.llm_parser:
                validation["errors"].append("LLM enabled but parser not initialized")
                validation["valid"] = False

            if self.orchestrator.enable_clarifier and not self.orchestrator.clarifier:
                validation["errors"].append("Clarifier enabled but not initialized")
                validation["valid"] = False

        except Exception as e:
            validation["valid"] = False
            validation["errors"].append(f"Configuration validation failed: {e}")

        return validation

    def get_diagnostic_info(self) -> Dict[str, Any]:
        """Get comprehensive diagnostic information."""
        return {
            "health_check": self.health_check(),
            "configuration_validation": self.validate_configuration(),
            "performance_stats": self.orchestrator.get_performance_stats(),
            "component_info": {
                "rule_count": self.orchestrator.rule_engine.get_rule_count() if self.orchestrator.rule_engine else 0,
                "cache_available": self.orchestrator.query_cache is not None,
                "llm_enabled": self.orchestrator.enable_llm,
                "clarifier_enabled": self.orchestrator.enable_clarifier,
            }
        }
