"""
Manager initialization for intent parsing orchestration.

This module handles the initialization of all manager classes that
coordinate different aspects of the orchestration.
"""

import logging
from dataclasses import dataclass
from typing import TYPE_CHECKING, Dict, Any

from .orchestrator_session import OrchestrationSessionManager
from .orchestrator_health import OrchestrationHealthManager
from .orchestrator_flow import OrchestrationFlowManager
from .orchestrator_utils import OrchestrationUtils

if TYPE_CHECKING:
    from .orchestrator_core import ParseOrchestrator

logger = logging.getLogger(__name__)


@dataclass
class OrchestrationManagers:
    """Container for all orchestration managers."""
    
    session_manager: OrchestrationSessionManager
    health_manager: OrchestrationHealthManager
    flow_manager: OrchestrationFlowManager
    utils: OrchestrationUtils

    @classmethod
    def create(cls, orchestrator: "ParseOrchestrator") -> "OrchestrationManagers":
        """
        Create and initialize all orchestration managers.
        
        Args:
            orchestrator: Reference to the main orchestrator
            
        Returns:
            Initialized OrchestrationManagers instance
        """
        logger.debug("Initializing orchestration managers...")
        
        # Initialize managers
        session_manager = OrchestrationSessionManager(orchestrator)
        health_manager = OrchestrationHealthManager(orchestrator)
        flow_manager = OrchestrationFlowManager(orchestrator)
        utils = OrchestrationUtils()
        
        managers = cls(
            session_manager=session_manager,
            health_manager=health_manager,
            flow_manager=flow_manager,
            utils=utils,
        )
        
        logger.debug("Orchestration managers initialized successfully")
        return managers

    def validate_managers(self) -> Dict[str, Any]:
        """
        Validate all managers are properly initialized.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "valid": True,
            "errors": [],
            "manager_status": {}
        }
        
        # Check all managers
        managers_to_check = [
            ("session_manager", self.session_manager),
            ("health_manager", self.health_manager),
            ("flow_manager", self.flow_manager),
            ("utils", self.utils),
        ]
        
        for name, manager in managers_to_check:
            if manager is None:
                validation["errors"].append(f"{name} is not initialized")
                validation["valid"] = False
            validation["manager_status"][name] = manager is not None
        
        return validation

    def get_session_context(self, tenant_id: str = None, user_id: str = None):
        """Delegate to session manager."""
        return self.session_manager.get_session_context(tenant_id, user_id)

    def check_clarifier_max_rounds(self, clarifier_rounds: int) -> bool:
        """Delegate to session manager."""
        return self.session_manager.check_clarifier_max_rounds(clarifier_rounds)

    def reset_clarifier_rounds(self, tenant_id: str = None, user_id: str = None):
        """Delegate to session manager."""
        self.session_manager.reset_clarifier_rounds(tenant_id, user_id)

    def increment_clarifier_rounds(self, tenant_id: str = None, user_id: str = None):
        """Delegate to session manager."""
        self.session_manager.increment_clarifier_rounds(tenant_id, user_id)

    def health_check(self):
        """Delegate to health manager."""
        return self.health_manager.health_check()

    def validate_configuration(self):
        """Delegate to health manager."""
        return self.health_manager.validate_configuration()

    def get_diagnostic_info(self):
        """Delegate to health manager."""
        return self.health_manager.get_diagnostic_info()

    def try_cache(self, *args, **kwargs):
        """Delegate to flow manager."""
        return self.flow_manager.try_cache(*args, **kwargs)

    def try_rule_engine(self, *args, **kwargs):
        """Delegate to flow manager."""
        return self.flow_manager.try_rule_engine(*args, **kwargs)

    def try_llm_parser(self, *args, **kwargs):
        """Delegate to flow manager."""
        return self.flow_manager.try_llm_parser(*args, **kwargs)

    def handle_fallback(self, *args, **kwargs):
        """Delegate to flow manager."""
        return self.flow_manager.handle_fallback(*args, **kwargs)

    def get_performance_stats(self, orchestrator):
        """Delegate to utils."""
        return self.utils.get_performance_stats(orchestrator)

    def finalize_result(self, result, metadata):
        """Delegate to utils."""
        return self.utils.finalize_result(result, metadata)

    def load_rules(self, orchestrator):
        """Delegate to utils."""
        return self.utils.load_rules(orchestrator)

    def cleanup_resources(self, orchestrator):
        """Delegate to utils."""
        return self.utils.cleanup_resources(orchestrator)

    def get_debug_info(self, orchestrator, message: str, tenant_id: str = None):
        """Delegate to utils."""
        return self.utils.get_debug_info(orchestrator, message, tenant_id)
