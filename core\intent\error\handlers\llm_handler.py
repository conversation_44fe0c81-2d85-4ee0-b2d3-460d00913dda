"""
LLM error handler for intent parsing.

This module provides specialized error handling for LLM
components and operations.
"""

import logging
from typing import Dict, Optional, Any

from .base_handler import ComponentErrorHandler
from ..error_types import ErrorSeverity

logger = logging.getLogger(__name__)


class LLMErrorHandler(ComponentErrorHandler):
    """Specialized error handler for LLM components."""

    def __init__(self, error_collector=None):
        super().__init__("llm", error_collector)

    def handle_llm_api_error(
        self,
        error: Exception,
        model: str,
        message: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle LLM API errors."""
        context = {
            "model": model,
            "user_message": message[:100],
            "error_type": "llm_api"
        }
        
        def recovery():
            # Attempt fallback model
            logger.info(f"Attempting LLM recovery with fallback model")
            return None
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.HIGH,
            recovery_function=recovery,
            tenant_id=tenant_id,
            user_message=message
        )

    def handle_llm_timeout_error(
        self,
        error: Exception,
        model: str,
        timeout_seconds: float,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle LLM timeout errors."""
        context = {
            "model": model,
            "timeout_seconds": timeout_seconds,
            "error_type": "llm_timeout"
        }
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            tenant_id=tenant_id
        )

    def handle_llm_rate_limit_error(
        self,
        error: Exception,
        model: str,
        retry_after: Optional[float] = None,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle LLM rate limit errors."""
        context = {
            "model": model,
            "retry_after": retry_after,
            "error_type": "llm_rate_limit"
        }
        
        def recovery():
            # Implement exponential backoff
            logger.info(f"Implementing rate limit recovery for {model}")
            return {"action": "retry_with_backoff", "retry_after": retry_after}
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def handle_llm_token_limit_error(
        self,
        error: Exception,
        model: str,
        token_count: int,
        max_tokens: int,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle LLM token limit errors."""
        context = {
            "model": model,
            "token_count": token_count,
            "max_tokens": max_tokens,
            "error_type": "llm_token_limit"
        }
        
        def recovery():
            # Attempt to truncate input
            logger.info(f"Attempting token limit recovery for {model}")
            return {"action": "truncate_input", "max_tokens": max_tokens}
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def get_llm_error_patterns(self) -> Dict[str, Any]:
        """
        Get common LLM error patterns.
        
        Returns:
            Dictionary with LLM error pattern analysis
        """
        llm_errors = self.error_collector.get_errors_by_component(self.component_name)
        
        patterns = {
            "api_errors": 0,
            "timeout_errors": 0,
            "rate_limit_errors": 0,
            "token_limit_errors": 0,
            "model_distribution": {},
            "common_patterns": []
        }
        
        for error in llm_errors:
            error_type = error.context.get("error_type", "unknown")
            model = error.context.get("model", "unknown")
            
            if error_type == "llm_api":
                patterns["api_errors"] += 1
            elif error_type == "llm_timeout":
                patterns["timeout_errors"] += 1
            elif error_type == "llm_rate_limit":
                patterns["rate_limit_errors"] += 1
            elif error_type == "llm_token_limit":
                patterns["token_limit_errors"] += 1
            
            # Track model distribution
            patterns["model_distribution"][model] = patterns["model_distribution"].get(model, 0) + 1
        
        # Identify common patterns
        if patterns["rate_limit_errors"] > 10:
            patterns["common_patterns"].append("Frequent rate limiting issues")
        
        if patterns["timeout_errors"] > 5:
            patterns["common_patterns"].append("LLM timeout issues")
        
        if patterns["token_limit_errors"] > 3:
            patterns["common_patterns"].append("Token limit exceeded frequently")
        
        return patterns

    def validate_llm_health(self) -> Dict[str, Any]:
        """
        Validate LLM specific health.
        
        Returns:
            Dictionary with LLM health validation
        """
        base_validation = self.validate_component_health()
        llm_patterns = self.get_llm_error_patterns()
        
        # Add LLM-specific health checks
        if llm_patterns["api_errors"] > 10:
            base_validation["errors"].append("High LLM API failure rate")
            base_validation["healthy"] = False
            base_validation["health_score"] -= 30
        
        if llm_patterns["rate_limit_errors"] > 10:
            base_validation["warnings"].append("Frequent rate limiting")
            base_validation["health_score"] -= 15
        
        if llm_patterns["timeout_errors"] > 5:
            base_validation["warnings"].append("LLM timeout issues")
            base_validation["health_score"] -= 10
        
        base_validation["llm_patterns"] = llm_patterns
        base_validation["health_score"] = max(base_validation["health_score"], 0)
        
        return base_validation
