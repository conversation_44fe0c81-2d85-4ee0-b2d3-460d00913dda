"""
Pattern matching and entity extraction for rule-based intent detection.

This module handles the core pattern matching logic and entity extraction.
"""

import re
import time
import logging
from typing import Dict, List, Optional, Any
from utils.text_utils import normalize_text
from .rule_models import IntentRule, RuleMatch

logger = logging.getLogger(__name__)


class RuleMatcher:
    """Handles pattern matching and entity extraction for rules."""

    def __init__(self):
        """Initialize the rule matcher."""
        self.compiled_patterns_cache: Dict[str, List[re.Pattern]] = {}

    def match_rule(
        self, rule: IntentRule, normalized_message: str, original_message: str
    ) -> Optional[RuleMatch]:
        """
        Try to match a single rule against the message.

        Args:
            rule: Rule to match against
            normalized_message: Normalized message text
            original_message: Original message text

        Returns:
            RuleMatch if successful, None otherwise
        """
        if not rule.compiled_patterns:
            return None

        for i, pattern in enumerate(rule.compiled_patterns):
            match = pattern.search(normalized_message)
            if match:
                # Extract entities from regex groups
                entities = self.extract_entities(rule, match, original_message)

                # Calculate confidence based on match quality
                confidence = self.calculate_confidence(match, normalized_message, rule)

                # Generate reasoning for why this rule matched
                reasoning = self._generate_match_reasoning(rule, match, entities, confidence, i)

                return RuleMatch(
                    rule_id=rule.id,
                    intent=rule.intent,
                    confidence=confidence,
                    entities=entities,
                    pattern_matched=rule.patterns[i],
                    processing_time_ms=0.0,  # Will be set by caller
                    source=rule.source,  # Include rule source for observability
                    reasoning=reasoning,  # Explanation of why this rule matched
                )

        return None

    def _generate_match_reasoning(
        self, rule: IntentRule, match: re.Match, entities: Dict[str, Any], confidence: float, pattern_index: int
    ) -> str:
        """Generate reasoning explanation for why a rule matched."""
        reasoning_parts = []

        # Basic match info
        reasoning_parts.append(f"Matched pattern #{pattern_index + 1}: '{rule.patterns[pattern_index]}'")

        # Confidence explanation
        if confidence >= 0.9:
            reasoning_parts.append("High confidence due to exact pattern match")
        elif confidence >= 0.7:
            reasoning_parts.append("Good confidence with clear pattern alignment")
        else:
            reasoning_parts.append("Lower confidence due to partial pattern match")

        # Entity extraction info
        if entities:
            entity_names = list(entities.keys())
            reasoning_parts.append(f"Extracted entities: {', '.join(entity_names)}")
        else:
            reasoning_parts.append("No entities extracted")

        # Rule source info
        source_info = f"from {rule.source} rules" if rule.source else "from rules"
        reasoning_parts.append(f"Rule {rule.id} {source_info}")

        return "; ".join(reasoning_parts)

    def extract_entities(
        self, rule: IntentRule, match: re.Match, original_message: str
    ) -> Dict[str, Any]:
        """
        Extract entities from regex match groups.

        Args:
            rule: Rule containing entity definitions
            match: Regex match object
            original_message: Original message text

        Returns:
            Dictionary of extracted entities
        """
        entities = {}

        try:
            # Extract named groups from regex match
            named_groups = match.groupdict()

            for entity_name, entity_config in rule.entities.items():
                entity_value = None

                # Try to get value from named group
                if entity_name in named_groups:
                    entity_value = named_groups[entity_name]

                # Try to get value from numbered group
                elif "group" in entity_config:
                    group_num = entity_config["group"]
                    if isinstance(group_num, int) and group_num <= len(match.groups()):
                        entity_value = match.group(group_num)

                # Apply entity processing if value found
                if entity_value:
                    entities[entity_name] = self.process_entity_value(
                        entity_value, entity_config, original_message
                    )

        except Exception as e:
            logger.warning(f"Entity extraction failed for rule {rule.id}: {e}")

        return entities

    def process_entity_value(
        self, value: str, entity_config: Dict[str, Any], original_message: str
    ) -> Any:
        """
        Process extracted entity value according to configuration.

        Args:
            value: Raw extracted value
            entity_config: Entity configuration
            original_message: Original message text

        Returns:
            Processed entity value
        """
        try:
            # Clean the value
            processed_value = value.strip()

            # Apply type conversion
            entity_type = entity_config.get("type", "string")
            if entity_type == "integer":
                processed_value = int(processed_value)
            elif entity_type == "float":
                processed_value = float(processed_value)
            elif entity_type == "boolean":
                processed_value = processed_value.lower() in ["true", "yes", "1", "on"]

            # Apply transformations
            transformations = entity_config.get("transformations", [])
            for transform in transformations:
                if transform == "lowercase":
                    processed_value = str(processed_value).lower()
                elif transform == "uppercase":
                    processed_value = str(processed_value).upper()
                elif transform == "title":
                    processed_value = str(processed_value).title()

            # Apply validation
            if "validation" in entity_config:
                validation = entity_config["validation"]
                if not self.validate_entity_value(processed_value, validation):
                    logger.warning(
                        f"Entity validation failed for value: {processed_value}"
                    )
                    return None

            return processed_value

        except Exception as e:
            logger.warning(f"Entity processing failed: {e}")
            return value  # Return original value if processing fails

    def validate_entity_value(self, value: Any, validation: Dict[str, Any]) -> bool:
        """
        Validate entity value against validation rules.

        Args:
            value: Value to validate
            validation: Validation configuration

        Returns:
            True if valid, False otherwise
        """
        try:
            # Check minimum/maximum for numeric values
            if isinstance(value, (int, float)):
                if "min" in validation and value < validation["min"]:
                    return False
                if "max" in validation and value > validation["max"]:
                    return False

            # Check length for string values
            if isinstance(value, str):
                if "min_length" in validation and len(value) < validation["min_length"]:
                    return False
                if "max_length" in validation and len(value) > validation["max_length"]:
                    return False

            # Check allowed values
            if "allowed_values" in validation:
                if value not in validation["allowed_values"]:
                    return False

            # Check regex pattern
            if "pattern" in validation and isinstance(value, str):
                pattern = re.compile(validation["pattern"])
                if not pattern.match(value):
                    return False

            return True

        except Exception as e:
            logger.warning(f"Entity validation error: {e}")
            return False

    def calculate_confidence(
        self, match: re.Match, message: str, rule: IntentRule
    ) -> float:
        """
        Calculate confidence score based on match quality.

        Args:
            match: Regex match object
            message: Normalized message
            rule: Matched rule

        Returns:
            Confidence score between 0.0 and 1.0
        """
        try:
            # Base confidence for any match
            base_confidence = 0.85

            # Adjust based on match coverage (how much of the message was matched)
            match_length = len(match.group(0))
            message_length = len(message)
            coverage_ratio = match_length / message_length if message_length > 0 else 0

            # Boost confidence for higher coverage
            coverage_boost = min(0.1, coverage_ratio * 0.15)

            # Adjust based on rule priority (lower priority number = higher confidence)
            # Handle both numeric and string priorities
            if isinstance(rule.priority, str):
                priority_map = {"high": 10, "medium": 50, "low": 90}
                priority_value = priority_map.get(rule.priority.lower(), 50)
            else:
                priority_value = rule.priority

            priority_adjustment = max(0, (100 - priority_value) / 1000)

            # Adjust based on number of captured groups (more specific patterns)
            groups_boost = min(0.05, len(match.groups()) * 0.01)

            # Calculate final confidence
            confidence = (
                base_confidence + coverage_boost + priority_adjustment + groups_boost
            )

            # Ensure confidence is within valid range
            return min(1.0, max(0.0, confidence))

        except Exception as e:
            logger.warning(f"Confidence calculation failed: {e}")
            return 0.8  # Default confidence

    def compile_patterns(self, patterns: List[str]) -> List[re.Pattern]:
        """
        Compile regex patterns with error handling.

        Args:
            patterns: List of pattern strings

        Returns:
            List of compiled regex patterns
        """
        compiled_patterns = []

        for pattern in patterns:
            try:
                # Compile with case-insensitive flag for better matching
                compiled_pattern = re.compile(pattern, re.IGNORECASE | re.UNICODE)
                compiled_patterns.append(compiled_pattern)
            except re.error as e:
                logger.error(f"Failed to compile pattern '{pattern}': {e}")

        return compiled_patterns

    def test_pattern(self, pattern: str, test_messages: List[str]) -> Dict[str, Any]:
        """
        Test a pattern against multiple messages.

        Args:
            pattern: Pattern string to test
            test_messages: List of test messages

        Returns:
            Dictionary with test results
        """
        try:
            compiled_pattern = re.compile(pattern, re.IGNORECASE | re.UNICODE)
            results = {
                "pattern": pattern,
                "matches": [],
                "total_tests": len(test_messages),
                "match_count": 0,
            }

            for message in test_messages:
                normalized_message = normalize_text(message, aggressive=False)
                match = compiled_pattern.search(normalized_message)

                if match:
                    results["matches"].append(
                        {
                            "message": message,
                            "matched_text": match.group(0),
                            "groups": match.groups(),
                            "groupdict": match.groupdict(),
                        }
                    )
                    results["match_count"] += 1

            results["match_rate"] = (
                results["match_count"] / results["total_tests"]
                if results["total_tests"] > 0
                else 0
            )

            return results

        except Exception as e:
            return {
                "pattern": pattern,
                "error": str(e),
                "matches": [],
                "total_tests": len(test_messages),
                "match_count": 0,
                "match_rate": 0.0,
            }
