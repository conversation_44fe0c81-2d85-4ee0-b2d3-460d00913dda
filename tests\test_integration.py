def test_integration_full_flow(mocker):
    cache_manager = mocker.Mo<PERSON>()
    cache_manager.check.return_value = None

    rag_retriever = mocker.Mock()
    rag_retriever.retrieve.return_value = "retrieved context"

    prompt_engine = mocker.Mock()
    prompt_engine.generate.return_value = "generated prompt"

    business_api = mocker.Mock()
    business_api.call.return_value = "api output"

    fallback_handler = mocker.Mock()
    fallback_handler.handle.return_value = "fallback response"

    orchestrator = AgentOrchestrator(cache_manager, rag_retriever, prompt_engine, business_api, fallback_handler)
    result = orchestrator.process_user_message("test message")

    assert result == "api output"


def test_integration_fallback_flow(mocker):
    cache_manager = mocker.Mock()
    cache_manager.check.side_effect = Exception("Simulated error")

    fallback_handler = mocker.Mock()
    fallback_handler.handle.return_value = "fallback handled"

    orchestrator = AgentOrchestrator(cache_manager, mocker.<PERSON><PERSON>(), mocker.<PERSON><PERSON>(), mocker.<PERSON><PERSON>(), fallback_handler)
    result = orchestrator.process_user_message("test error")

    assert result == "fallback handled"
