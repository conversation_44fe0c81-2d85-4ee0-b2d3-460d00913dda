"""
Prompt building for LLM intent parsing.

This module handles prompt construction and management for LLM calls.
"""

import json
import logging
from typing import Dict, Optional, Any
from config.tenant_config import get_tenant_metadata

# Prompt Engine integration
from core.prompts.prompt_engine import PromptEngine
from core.prompts.template_store import TemplateStore
from core.prompts.prompt_builder import PromptBuilder
from core.prompts.mock_template_store import MockTemplateStore
from clients.elixir_api import ElixirAPI

logger = logging.getLogger(__name__)


class LLMPromptBuilder:
    """Builds prompts for LLM intent parsing."""

    def __init__(self, prompt_engine: Optional[PromptEngine] = None):
        """
        Initialize the prompt builder.

        Args:
            prompt_engine: Optional PromptEngine instance for template-based prompts
        """
        # Standardized intent categories aligned with requirements
        self.base_intents = [
            "OrderStatus",
            "ProductLookup",
            "CancelOrder",
            "CheckDiscount",
            "CheckAvailability",
            "ModifyOrder",
            "PaymentIssue",
            "RefundStatus",
            "BrowseCategory",
            "ShippingInfo",
            "Unknown",
        ]

        # Initialize PromptEngine if provided, otherwise create default
        self.prompt_engine = prompt_engine or self._create_default_prompt_engine()

    def _create_default_prompt_engine(self) -> Optional[PromptEngine]:
        """Create default PromptEngine instance, return None if not available."""
        try:
            # Import here to avoid circular imports
            from core.rag.db import db_pool

            # Try to create PromptEngine with database store first
            try:
                prompt_builder = PromptBuilder()
                prompt_engine = PromptEngine.create_with_fallback(
                    db_pool=db_pool,
                    prompt_builder=prompt_builder,
                    llm_client=None  # No LLM client needed for prompt building
                )
                logger.info("Created PromptEngine with database/fallback support")
                return prompt_engine
            except Exception as e:
                logger.warning(f"Failed to create enhanced PromptEngine, trying legacy approach: {e}")

                # Fallback to legacy ElixirAPI approach
                try:
                    elixir_api = ElixirAPI()
                    template_store = TemplateStore(elixir_api)
                    prompt_builder = PromptBuilder()
                    logger.info("Using ElixirAPI-based TemplateStore for PromptEngine (legacy fallback)")
                    return PromptEngine(template_store, prompt_builder, None)
                except Exception as e2:
                    logger.info(f"ElixirAPI not available, using MockTemplateStore: {e2}")
                    template_store = MockTemplateStore()
                    prompt_builder = PromptBuilder()
                    return PromptEngine(template_store, prompt_builder, None)

        except Exception as e:
            logger.warning(f"Failed to create PromptEngine: {e}")
            return None

    def build_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Build the prompt for LLM intent parsing.

        Args:
            message: User message to classify
            tenant_id: Tenant identifier for context
            context: Additional context information

        Returns:
            Formatted prompt string
        """
        # If session context is present, use inline prompt for better conversation handling
        if context and "session_context" in context and context["session_context"]:
            logger.debug("Session context detected, using inline prompt for conversation handling")
            return self._build_inline_prompt(message, tenant_id, context)

        # Try to use PromptEngine first for regular requests
        if self.prompt_engine:
            try:
                return self._build_prompt_with_engine(message, tenant_id, context)
            except Exception as e:
                logger.warning(f"PromptEngine failed, falling back to inline prompt: {e}")

        # Fallback to inline prompt construction
        return self._build_inline_prompt(message, tenant_id, context)

    def _build_prompt_with_engine(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Build prompt using PromptEngine template system."""
        # Get tenant metadata for context
        tenant_metadata = get_tenant_metadata(tenant_id or "default")
        tenant_intents = tenant_metadata.get("custom_intents", [])
        all_intents = self.base_intents + tenant_intents
        vertical = tenant_metadata.get("vertical", "general")

        # Prepare conversation context for template
        conversation_context = ""
        if context and "session_context" in context:
            session_context = context["session_context"]
            if session_context.get("context_entities"):
                conversation_context += f"Previous entities: {json.dumps(session_context['context_entities'])}\n"
            if session_context.get("last_query"):
                conversation_context += f"Previous query: {session_context['last_query']}\n"

        # Prepare inputs for template
        # Extract clarifier_rounds from session context to bias LLM behavior and prevent loops - FIXED
        clarifier_rounds = 0
        if context and "session_context" in context:
            clarifier_rounds = context["session_context"].get("clarifier_rounds", 0)

        inputs = {
            "message": message,
            "tenant_id": tenant_id or "default",
            "context": json.dumps(context) if context else "",
            "conversation_context": conversation_context,
            "intents": ", ".join(all_intents),
            "vertical": vertical,
            "clarifier_rounds": clarifier_rounds,
        }

        # Use PromptEngine to get formatted prompt with tenant support
        return self.prompt_engine.get("classify_intent", inputs, tenant_id)

    def _build_inline_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Build prompt using inline construction (fallback method)."""
        # Add tenant-specific intents using configuration
        tenant_metadata = get_tenant_metadata(tenant_id or "default")
        tenant_intents = tenant_metadata.get("custom_intents", [])

        all_intents = self.base_intents + tenant_intents

        # Build context information using tenant metadata
        context_info = ""
        if tenant_id:
            vertical = tenant_metadata.get("vertical", "unknown")
            if vertical != "unknown":
                context_info += f"Tenant: {tenant_id} ({vertical} store)\n"
            else:
                context_info += f"Tenant: {tenant_id}\n"

        # Add conversation context for multi-turn conversations
        if context and "session_context" in context:
            session_context = context["session_context"]
            if session_context.get("context_entities"):
                context_info += f"Previous entities from conversation: {json.dumps(session_context['context_entities'])}\n"
            if session_context.get("last_query"):
                context_info += f"Previous query: {session_context['last_query']}\n"

        if context:
            # Extract clarifier_rounds from context and include in prompt to prevent loops - FIXED
            clarifier_rounds = 0
            if context.get("session_context"):
                clarifier_rounds = context["session_context"].get("clarifier_rounds", 0)
                if clarifier_rounds > 0:
                    context_info += f"Previous clarification attempts: {clarifier_rounds} (avoid repetitive questions)\n"

            # Filter out session_context to avoid duplication
            filtered_context = {k: v for k, v in context.items() if k != "session_context"}
            if filtered_context:
                context_info += f"Additional context: {json.dumps(filtered_context)}\n"

        prompt = f"""Classify user message into intent. {context_info}
Intents: {', '.join(all_intents)}
Message: "{message}"

IMPORTANT: If previous entities are provided, merge them with new entities from the current message. For follow-up requests like "and with that i want...", combine all entities from the conversation.

Respond with JSON: {{"intent": "...", "confidence": 0.0-1.0, "entities": {{}}, "reasoning": "..."}}"""

        # TODO: Ensure no trailing \n or malformed JSON gets into final prompt strings. Right now it's okay, but defensive trimming at the end can help:
        # return prompt.strip()
        return prompt

    def build_system_message(self) -> str:
        """
        Build the system message for LLM calls.

        Returns:
            System message string
        """
        return "You are a precise intent classification assistant. Always respond with valid JSON."

    def get_intent_descriptions(
        self, tenant_id: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Get descriptions for available intents.

        Args:
            tenant_id: Tenant identifier for context

        Returns:
            Dictionary mapping intent names to descriptions
        """
        descriptions = {
            "OrderStatus": "Check the status of an existing order",
            "ProductLookup": "Search for or get information about products",
            "CancelOrder": "Cancel an existing order",
            "CheckDiscount": "Check for available discounts or promotions",
            "CheckAvailability": "Check if a product is available or in stock",
            "ModifyOrder": "Modify an existing order (change items, quantity, etc.)",
            "PaymentIssue": "Issues related to payment processing",
            "RefundStatus": "Check the status of a refund",
            "BrowseCategory": "Browse products by category",
            "ShippingInfo": "Get information about shipping options or status",
            "Unknown": "Intent cannot be determined or doesn't match any category",
        }

        # Add tenant-specific intent descriptions if available
        if tenant_id:
            tenant_metadata = get_tenant_metadata(tenant_id)
            tenant_descriptions = tenant_metadata.get("intent_descriptions", {})
            # TODO: Avoid .update() on session dicts - use explicit field assignment to prevent accidental overwrites - Production Blocker
            descriptions.update(tenant_descriptions)

        return descriptions

    def build_enhanced_prompt(
        self,
        message: str,
        tenant_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        include_examples: bool = False,
    ) -> str:
        """
        Build an enhanced prompt with examples and detailed instructions.

        Args:
            message: User message to classify
            tenant_id: Tenant identifier for context
            context: Additional context information
            include_examples: Whether to include example classifications

        Returns:
            Enhanced prompt string
        """
        # Get intent descriptions
        intent_descriptions = self.get_intent_descriptions(tenant_id)

        # Build tenant context
        tenant_metadata = get_tenant_metadata(tenant_id or "default")
        vertical = tenant_metadata.get("vertical", "general")

        prompt = f"""You are an expert intent classifier for a {vertical} business.

Available intents and their descriptions:
"""

        # Add intent descriptions
        for intent, description in intent_descriptions.items():
            prompt += f"- {intent}: {description}\n"

        # Add conversation context for multi-turn conversations
        if context and "session_context" in context:
            session_context = context["session_context"]
            if session_context.get("context_entities"):
                prompt += f"\nPrevious entities from conversation: {json.dumps(session_context['context_entities'])}\n"
            if session_context.get("last_query"):
                prompt += f"Previous query: {session_context['last_query']}\n"

        # Add other context if provided
        if context:
            # TODO: Filter out session_context to avoid duplication
            # filtered_context = {k: v for k, v in context.items() if k != "session_context"}
            # if filtered_context:
            #     context_info += f"Additional context: {json.dumps(filtered_context)}\n"
            #
            # TODO: def format_session_context(session_context):
            #     parts = []
            #     if session_context.get("context_entities"):
            #         parts.append(f"Previous entities: {json.dumps(session_context['context_entities'])}")
            #     if session_context.get("last_query"):
            #         parts.append(f"Previous query: {session_context['last_query']}")
            #     return "\n".join(parts)
            # This will reduce duplication across _build_inline_prompt() and build_enhanced_prompt().
            #
            # Show clarifier_rounds to bias the LLM and avoid loops - FIXED
            if session_context.get("clarifier_rounds", 0) > 0:
                prompt += f"\nPrevious clarification attempts: {session_context['clarifier_rounds']} (provide direct answer if possible)\n"

            # Filter out session_context to avoid duplication
            filtered_context = {k: v for k, v in context.items() if k != "session_context"}
            if filtered_context:
                prompt += f"\nContext: {json.dumps(filtered_context)}\n"

        # Add examples if requested
        if include_examples:
            prompt += self._get_examples(vertical)

        # TODO: Add enhanced multi-locale support in prompts with examples for "hi-en", "hi", "en" - Future Enhancement
        prompt += f"""
User message: "{message}"

Classify this message and respond with JSON in this exact format:
{{"intent": "intent_name", "confidence": 0.95, "entities": {{}}, "reasoning": "brief explanation"}}

Guidelines:
- Confidence should be 0.0-1.0 (use 0.8+ for clear matches, 0.5-0.8 for uncertain, <0.5 for very unclear)
- Extract relevant entities into the entities object
- If previous entities are provided, merge them with new entities from the current message
- For follow-up requests like "and with that i want...", combine all entities from the conversation
- Provide brief reasoning for your classification
- Use "Unknown" if the intent is unclear or doesn't match any category
"""

        return prompt

    def _get_examples(self, vertical: str) -> str:
        """Get example classifications for the given vertical."""
        if vertical == "furniture":
            return """
Examples:
- "Where is my sofa order?" → OrderStatus
- "Show me dining tables" → ProductLookup
- "Do you have this chair in blue?" → CheckAvailability
- "I want to cancel my bed order" → CancelOrder
"""
        elif vertical == "clothing":
            return """
Examples:
- "Track my shirt order" → OrderStatus
- "Show me winter jackets" → ProductLookup
- "Do you have size M in this dress?" → CheckAvailability
- "Cancel my pants order" → CancelOrder
"""
        else:
            return """
Examples:
- "Where is my order?" → OrderStatus
- "Show me products" → ProductLookup
- "Is this item available?" → CheckAvailability
- "I want to cancel my order" → CancelOrder
"""

    def validate_prompt_length(self, prompt: str, max_tokens: int = 1000) -> bool:
        """
        Validate that the prompt is within token limits.

        Args:
            prompt: Prompt string to validate
            max_tokens: Maximum allowed tokens (rough estimate)

        Returns:
            True if prompt is within limits
        """
        # Rough estimation: 1 token ≈ 4 characters
        estimated_tokens = len(prompt) / 4
        return estimated_tokens <= max_tokens
