"""
Base error handler for intent parsing components.

This module provides the base error handling functionality that
all component-specific handlers inherit from.
"""

import logging
from typing import Dict, Optional, Any, Callable

from ..error_types import Error<PERSON>ategory, ErrorSeverity
from ..error_collector import get_error_collector

logger = logging.getLogger(__name__)


class ComponentErrorHandler:
    """Base class for component-specific error handlers."""

    def __init__(self, component_name: str, error_collector=None):
        """
        Initialize error handler.
        
        Args:
            component_name: Name of the component this handler manages
            error_collector: Error collector instance (uses global if None)
        """
        self.component_name = component_name
        self.error_collector = error_collector or get_error_collector()

    def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        recovery_function: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Handle an error with tracking and optional recovery.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            severity: Error severity level
            recovery_function: Optional recovery function to attempt
            **kwargs: Additional context for error tracking
            
        Returns:
            Dictionary with error handling results
        """
        # Determine error category based on component
        category = self._get_error_category()
        
        # Create error event
        from ..error_types import ErrorEvent
        error_event = ErrorEvent.create(
            category=category,
            severity=severity,
            component=self.component_name,
            error=error,
            context=context,
            **kwargs
        )
        
        # Track the error
        error_id = self.error_collector.collect_error(error_event)
        
        # Attempt recovery if function provided
        recovery_result = None
        recovery_successful = False
        
        if recovery_function:
            try:
                recovery_result = recovery_function()
                recovery_successful = recovery_result is not None
                    
            except Exception as recovery_error:
                logger.error(f"Recovery failed for {self.component_name}: {recovery_error}")
                recovery_result = None
        
        return {
            "error_id": error_id,
            "component": self.component_name,
            "category": category.value,
            "severity": severity.value,
            "error_type": type(error).__name__,
            "message": str(error),
            "recovery_attempted": recovery_function is not None,
            "recovery_successful": recovery_successful,
            "recovery_result": recovery_result,
            "context": context or {}
        }

    def _get_error_category(self) -> ErrorCategory:
        """Get error category based on component name."""
        component_lower = self.component_name.lower()
        
        if "rule" in component_lower:
            return ErrorCategory.RULE_ENGINE
        elif "cache" in component_lower:
            return ErrorCategory.CACHE
        elif "llm" in component_lower:
            return ErrorCategory.LLM
        elif "clarifier" in component_lower:
            return ErrorCategory.CLARIFIER
        elif "session" in component_lower:
            return ErrorCategory.SESSION
        elif "orchestrat" in component_lower:
            return ErrorCategory.ORCHESTRATION
        else:
            return ErrorCategory.UNKNOWN

    def get_error_stats(self) -> Dict[str, Any]:
        """
        Get error statistics for this component.
        
        Returns:
            Dictionary with component error statistics
        """
        component_errors = self.error_collector.get_errors_by_component(self.component_name)
        
        if not component_errors:
            return {
                "component": self.component_name,
                "total_errors": 0,
                "recent_errors": 0,
                "error_types": {},
                "severity_distribution": {}
            }
        
        # Count by error type and severity
        error_types = {}
        severity_counts = {}
        recent_count = 0
        
        for error in component_errors:
            error_types[error.error_type] = error_types.get(error.error_type, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
            
            if error.is_recent(24):  # Last 24 hours
                recent_count += 1
        
        return {
            "component": self.component_name,
            "total_errors": len(component_errors),
            "recent_errors": recent_count,
            "error_types": error_types,
            "severity_distribution": severity_counts,
        }

    def validate_component_health(self) -> Dict[str, Any]:
        """
        Validate component health based on error patterns.
        
        Returns:
            Dictionary with health validation results
        """
        stats = self.get_error_stats()
        
        validation = {
            "healthy": True,
            "warnings": [],
            "errors": [],
            "health_score": 100
        }
        
        # Check recent error count
        if stats["recent_errors"] > 10:
            validation["warnings"].append(f"High recent error count: {stats['recent_errors']}")
            validation["health_score"] -= 20
        
        # Check for critical errors
        critical_count = stats["severity_distribution"].get("critical", 0)
        if critical_count > 0:
            validation["errors"].append(f"Critical errors detected: {critical_count}")
            validation["healthy"] = False
            validation["health_score"] -= 40
        
        # Check for high error count
        high_count = stats["severity_distribution"].get("high", 0)
        if high_count > 5:
            validation["warnings"].append(f"High severity errors: {high_count}")
            validation["health_score"] -= 15
        
        validation["health_score"] = max(validation["health_score"], 0)
        
        return validation
