from fastapi import APIRouter, Request, HTTPException
from schemas.message_schema import (
    IntentParseRequest,
    IntentClarificationRequest,
    IntentParseResponse,
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/intent", tags=["intent"])


@router.post("/parse", response_model=IntentParseResponse)
def parse_intent(request: IntentParseRequest, fastapi_request: Request):
    """
    Parse user message to extract intent and entities.

    This endpoint implements the complete intent parsing flow:
    1. Rule Engine (regex matching) - Fast, deterministic
    2. Query Cache (Redis lookup) - Fast, for repeated queries
    3. LLM Parser (GPT-4o/GPT-3.5) - Slower, for complex cases
    4. Clarifier (low confidence) - Interactive clarification system

    Args:
        request: Intent parsing request with message and optional parameters

    Returns:
        IntentParseResponse with parsed intent, confidence, entities, and metadata
    """
    try:
        # Get the intent parser from app state
        intent_parser = fastapi_request.app.state.intent_parser

        # Parse the intent using the complete flow
        result = intent_parser.parse_intent(
            message=request.message,
            tenant_id=request.tenant_id,
            session_id=request.session_id,
            user_id=request.user_id,
            locale=request.locale,
        )

        # Store entities in session context for conversation continuity
        if request.user_id and result.get("entities"):
            try:
                from core.session.session_state import SessionStore
                from clients.redis_client import RedisClient

                # Get session store (reuse existing Redis connection)
                redis_client = RedisClient()
                session_store = SessionStore(redis_client)

                # Get existing session context
                existing_context = session_store.get_context(
                    request.tenant_id or "default",
                    request.user_id
                )

                # Update session context with new entities and query - Session Dictionary Safety FIXED
                updated_context = existing_context.copy()
                updated_context["context_entities"] = result.get("entities", {})
                updated_context["last_query"] = request.message
                updated_context["last_intent"] = result.get("intent", "Unknown")

                # Save updated context
                session_store.save_context(
                    request.tenant_id or "default",
                    request.user_id,
                    updated_context
                )

                logger.debug(f"Stored entities in session context for user {request.user_id}")
            except Exception as e:
                logger.warning(f"Failed to store entities in session context: {e}")

        # Convert result to response format
        response = IntentParseResponse(
            status="success",
            intent=result.get("intent", "Unknown"),
            confidence=result.get("confidence", 0.0),
            entities=result.get("entities", {}),
            source=result.get("source", "unknown"),
            source_chain=result.get("source_chain", "unknown"),
            processing_time_ms=result.get("processing_time_ms", 0.0),
            session_id=result.get("session_id"),
            tenant_id=result.get("tenant_id"),
            clarification_needed=result.get("clarification_needed", False),
            clarification_prompt=result.get("clarification_prompt"),
            metadata={
                "rule_id": result.get("rule_id"),
                "rule_source": result.get(
                    "rule_source"
                ),  # "base" or "tenant" for observability
                "pattern_matched": result.get("pattern_matched"),
                "model_used": result.get("model_used"),
                "reasoning": result.get("reasoning"),
                "tokens_used": result.get("tokens_used"),
                "cache_key": result.get("cache_key"),
                "clarifier_rounds": result.get("clarifier_rounds", 0),
            },
        )

        logger.info(
            f"Intent parsed: {response.intent} (confidence: {response.confidence}, source: {response.source})"
        )
        print("[DEBUG] Response: {response}")
        return response

    except Exception as e:
        logger.error(f"Intent parsing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Intent parsing failed: {str(e)}")


@router.post("/clarify", response_model=IntentParseResponse)
def handle_clarification(request: IntentClarificationRequest, fastapi_request: Request):
    """
    Handle clarification response and re-parse intent with enhanced context.

    This endpoint is used when the initial intent parsing resulted in low confidence
    and clarification was needed. It processes the user's clarification response
    and re-parses the original message with enhanced context.

    Args:
        request: Clarification request with original message and clarification response

    Returns:
        IntentParseResponse with final parsed intent after clarification
    """
    try:
        # Get the intent parser from app state
        intent_parser = fastapi_request.app.state.intent_parser

        # Handle clarification and re-parse intent
        result = intent_parser.parse_intent_with_clarification_response(
            original_message=request.original_message,
            clarification_response=request.clarification_response,
            session_id=request.session_id,
            tenant_id=request.tenant_id,
            locale=request.locale,
        )

        # Convert result to response format
        response = IntentParseResponse(
            status="success",
            intent=result.get("intent", "Unknown"),
            confidence=result.get("confidence", 0.0),
            entities=result.get("entities", {}),
            source=result.get("source", "unknown"),
            source_chain=result.get("source_chain", "unknown"),
            processing_time_ms=result.get("processing_time_ms", 0.0),
            session_id=result.get("session_id"),
            tenant_id=result.get("tenant_id"),
            clarification_needed=result.get("clarification_needed", False),
            clarification_prompt=result.get("clarification_prompt"),
            error=result.get("error"),
            metadata={
                "rule_id": result.get("rule_id"),
                "rule_source": result.get(
                    "rule_source"
                ),  # "base" or "tenant" for observability
                "pattern_matched": result.get("pattern_matched"),
                "model_used": result.get("model_used"),
                "reasoning": result.get("reasoning"),
                "tokens_used": result.get("tokens_used"),
                "cache_key": result.get("cache_key"),
                "clarifier_rounds": result.get("clarifier_rounds", 0),
                "clarification_successful": result.get(
                    "clarification_successful", False
                ),
            },
        )

        logger.info(
            f"Clarification handled: {response.intent} (confidence: {response.confidence}, source: {response.source})"
        )
        return response

    except Exception as e:
        logger.error(f"Clarification handling failed: {e}")
        raise HTTPException(
            status_code=500, detail=f"Clarification handling failed: {str(e)}"
        )


@router.get("/stats")
def get_intent_parser_stats(fastapi_request: Request):
    """
    Get performance statistics from the intent parser.

    Returns:
        Dictionary with performance statistics including request counts,
        processing times, and component-specific metrics
    """
    try:
        # Get the intent parser from app state
        intent_parser = fastapi_request.app.state.intent_parser

        # Get comprehensive stats
        stats = intent_parser.get_performance_stats()

        return {"status": "success", "stats": stats}

    except Exception as e:
        logger.error(f"Failed to get intent parser stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")
