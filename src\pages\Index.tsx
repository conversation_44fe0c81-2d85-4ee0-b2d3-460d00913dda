
import React, { useEffect } from 'react';
import Navbar from '../components/Navbar';
import Hero from '../components/Hero';
import ProblemSolution from '../components/ProblemSolution';
import DemoSection from '../components/DemoSection';
import AIAgents from '../components/AIAgents';
import SocialProof from '../components/SocialProof';
import WaitlistForm from '../components/WaitlistForm';
import TrustStrip from '../components/TrustStrip';
import Footer from '../components/Footer';

const Index = () => {
  useEffect(() => {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        
        const targetId = this.getAttribute('href')?.substring(1);
        if (!targetId) return;
        
        const targetElement = document.getElementById(targetId);
        if (!targetElement) return;

        window.scrollTo({
          top: targetElement.offsetTop - 80, // Account for navbar height
          behavior: 'smooth'
        });
      });
    });

    return () => {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.removeEventListener('click', function() {});
      });
    };
  }, []);

  return (
    <div className="min-h-screen overflow-x-hidden bg-white">
      <Navbar />
      <Hero />
      <ProblemSolution />
      <DemoSection />
      <AIAgents />
      <SocialProof />
      <WaitlistForm />
      <TrustStrip />
      <Footer />
    </div>
  );
};

export default Index;
