import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Source color mapping for consistent UI theming
export const sourceColors = {
  rule: 'text-green-600 bg-green-50 border-green-200',
  cache: 'text-blue-600 bg-blue-50 border-blue-200',
  llm: 'text-orange-500 bg-orange-50 border-orange-200',
  clarifier: 'text-purple-600 bg-purple-50 border-purple-200',
  error: 'text-red-600 bg-red-50 border-red-200',
  default: 'text-gray-600 bg-gray-50 border-gray-200'
} as const

export function getSourceColor(source: string): string {
  return sourceColors[source as keyof typeof sourceColors] || sourceColors.default
}
