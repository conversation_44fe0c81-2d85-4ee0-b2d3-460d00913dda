import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Send, RotateCcw, Loader2, Eye, EyeOff } from 'lucide-react';
import { useIntentParser } from '@/hooks/useIntentParser';
import { useTestSession } from '@/context/TestSessionContext';
import PipelineInspector from '@/components/PipelineInspector';
import ResponseDisplay from '@/components/ResponseDisplay';
import { formatTimestamp, generateRequestId } from '@/utils/sessionUtils';

const IntentParserPanel: React.FC = () => {
  const [inputMessage, setInputMessage] = useState('');
  const { parseIntent, isLoading, error, debugInfo, clearError } = useIntentParser();
  const { intentSession, addIntentMessage, updateIntentMessageFeedback, resetIntentSession } = useTestSession();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');

    // Add user message to session
    addIntentMessage({
      type: 'user',
      content: userMessage,
    });

    // Clear any previous errors
    clearError();

    // Parse intent
    const response = await parseIntent({
      message: userMessage,
      user_id: `debug_user_${intentSession.id}`,
    });

    // Add response to session
    if (response) {
      const requestId = generateRequestId();
      addIntentMessage({
        type: 'agent',
        content: response,
        debug_info: debugInfo,
        request_id: requestId,
      });
    } else if (error) {
      addIntentMessage({
        type: 'agent',
        content: `Error: ${error}`,
        debug_info: debugInfo,
      });
    }
  };

  const handleReset = () => {
    resetIntentSession();
    setInputMessage('');
    clearError();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="h-full flex">
      {/* Chat Interface - 70% */}
      <div className="w-full md:w-[70%] flex flex-col bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Intent Parser Test</h2>
              <p className="text-sm text-gray-600">Test intent parsing capabilities</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </Button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {intentSession.messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-[#DCF8C6] text-gray-900'
                    : message.type === 'system'
                    ? 'bg-gray-200 text-gray-700 text-center text-sm'
                    : 'bg-white text-gray-900 shadow-sm border'
                }`}
              >
                {message.type === 'agent' && typeof message.content === 'object' ? (
                  // Render structured intent response with enhanced UI
                  <ResponseDisplay
                    content={message.content}
                    messageId={message.id}
                    requestId={message.request_id}
                    feedback={message.feedback}
                    onFeedbackChange={(feedback) => updateIntentMessageFeedback(message.id, feedback)}
                    type="intent"
                  />
                ) : (
                  // Render regular text content
                  <div className="whitespace-pre-wrap text-sm">
                    {typeof message.content === 'string' ? message.content : JSON.stringify(message.content)}
                  </div>
                )}
                <div className="text-xs text-gray-500 mt-2">
                  {formatTimestamp(message.timestamp)}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-white rounded-lg p-3 shadow-sm border">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">Parsing intent...</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Input */}
        <div className="bg-white border-t border-gray-200 p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter a message to parse intent..."
              className="flex-1"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={!inputMessage.trim() || isLoading}
              className="bg-teal-600 hover:bg-teal-700"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </form>
          
          {error && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Debug Sidebar - 30% */}
      <div className="hidden md:block w-[30%] border-l border-gray-200 bg-white p-4 overflow-y-auto">
        <PipelineInspector
          debugInfo={debugInfo}
          isLoading={isLoading}
          title="Intent Parser Debug"
        />
      </div>
    </div>
  );
};

export default IntentParserPanel;
