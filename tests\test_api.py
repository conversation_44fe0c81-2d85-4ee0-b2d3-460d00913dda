import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_ping():
    response = client.get("/ping")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_agent_ask(mocker):
    # Patch flow_router inside the sales_agent router
    from api.routers import sales_agent
    mock_flow_router = mocker.Mock()
    mock_flow_router.route.return_value = "mock agent response"
    sales_agent.flow_router = mock_flow_router

    payload = {
        "text": "What's my order status?",
        "user_id": "test_user"
    }
    response = client.post("/agent/ask", json=payload)

    assert response.status_code == 200
    json_data = response.json()
    assert json_data["status"] == "success"
    assert json_data["content"] == "mock agent response"
