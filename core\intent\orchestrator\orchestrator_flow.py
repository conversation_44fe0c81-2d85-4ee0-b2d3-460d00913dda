"""
Flow management for intent parsing orchestration.

This module contains the step-by-step flow methods for the orchestrator.
"""

import time
import logging
from typing import Dict, Optional, Any, List
from ..intent_config import FALLBACK_INTENT
from ..intent_utils import (
    log_parsing_result,
    log_clarifier_event,
    log_llm_event,
    log_general_event,
    should_trigger_clarifier,
    is_high_confidence,
    calculate_processing_time,
)
from ..response import ParseResponseBuilder
from ..intent_metadata import ParseMetadata

logger = logging.getLogger(__name__)


class OrchestrationFlowManager:
    """Manages the step-by-step flow of intent parsing orchestration."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

    def try_rule_engine(
        self,
        message: str,
        tenant_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Try to match intent using rule engine.

        Args:
            message: User message
            tenant_id: Tenant identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Rule match result or None if no match
        """
        rule_start = time.perf_counter()
        source_chain.append("rule")
        metadata.add_source("rule")
        # Enhanced source_chain debug tracing for complete flow path observability - FIXED

        try:
            rule_match = self.orchestrator.rule_engine.match(message, tenant_id, locale)

            metadata.rule_check_time_ms = calculate_processing_time(rule_start)

            if rule_match:
                metadata.rule_matched = True

                # Build response
                result = response_builder.build_rule_response(
                    rule_match, metadata.rule_check_time_ms, source_chain
                )

                # Cache successful rule matches for faster future lookups
                self.orchestrator.query_cache.set(message, result, tenant_id)

                # Update source chain to show caching occurred
                if len(source_chain) > 0 and source_chain[-1] == "rule":
                    source_chain[-1] = "rule → cache"

                log_parsing_result(
                    "rule_match", rule_match.intent, rule_match.confidence
                )
                return result

            else:
                log_general_event("rule_no_match", message=message[:50])
                return None

        except Exception as e:
            metadata.add_error(f"Rule engine failed: {str(e)}")
            metadata.rule_check_time_ms = calculate_processing_time(rule_start)
            log_general_event("rule_error", error=str(e))
            return None

    def try_cache(
        self,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Try to get cached result.

        Args:
            message: User message
            tenant_id: Tenant identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Cached result or None if no cache hit
        """
        cache_start = time.perf_counter()
        # Enhanced source chain tracking - show previous step if exists
        if source_chain:
            source_chain.append(f"{source_chain[-1]} → cache")
        else:
            source_chain.append("cache")
        metadata.add_source("cache")

        try:
            cached_result = self.orchestrator.query_cache.get(message, tenant_id)

            metadata.cache_check_time_ms = calculate_processing_time(cache_start)

            if cached_result:
                metadata.cache_hit = True

                # Update response with current session context
                result = response_builder.build_cache_response(
                    cached_result.__dict__, metadata.cache_check_time_ms, source_chain
                )

                log_parsing_result(
                    "cache_hit",
                    cached_result.intent,
                    cached_result.confidence,
                )
                return result

            else:
                log_general_event("cache_miss", message=message[:50])
                return None

        except Exception as e:
            metadata.add_error(f"Cache lookup failed: {str(e)}")
            metadata.cache_check_time_ms = calculate_processing_time(cache_start)
            log_general_event("cache_error", error=str(e))
            return None

    def try_llm_parser(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
        session_context: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Try LLM parser for intent detection.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            LLM parsing result or None if LLM disabled/failed
        """
        if not self.orchestrator.enable_llm or not self.orchestrator.llm_parser:
            log_general_event("llm_disabled")
            return None

        llm_start = time.perf_counter()
        source_chain.append("llm")
        metadata.add_source("llm")

        try:
            # Add original message and session context for LLM
            context = {
                "original_message": message,
                "locale": locale,
                "session_context": session_context or {}
            }

            llm_result = self.orchestrator.llm_parser.parse_intent(
                message, tenant_id, context
            )

            metadata.llm_call_time_ms = calculate_processing_time(llm_start)
            metadata.llm_called = True

            if llm_result:
                # Check if clarifier should be triggered
                clarification_needed = should_trigger_clarifier(llm_result.confidence)

                # Build response
                result = response_builder.build_llm_response(
                    llm_result,
                    metadata.llm_call_time_ms,
                    source_chain,
                    "llm",
                    clarification_needed,
                )

                # Handle clarifier if enabled and needed
                if (
                    self.orchestrator.enable_clarifier
                    and self.orchestrator.clarifier
                    and clarification_needed
                ):
                    # TODO: Update source_chain to show "llm → clarifier" flow for debug tracing - Production Blocker
                    # Increment clarifier rounds in session state
                    self.orchestrator._increment_clarifier_rounds(tenant_id, user_id)
                    return self._handle_clarifier_flow(
                        result, session_id, tenant_id, metadata, response_builder
                    )

                # Cache successful LLM results (only if high confidence)
                if is_high_confidence(llm_result.confidence):
                    self.orchestrator.query_cache.set(message, result, tenant_id)

                log_parsing_result(
                    "llm_success", llm_result.intent, llm_result.confidence
                )
                log_llm_event("success", model=llm_result.model_used)
                return result

            else:
                log_llm_event("failed")
                return None

        except Exception as e:
            metadata.add_error(f"LLM parsing failed: {str(e)}")
            metadata.llm_call_time_ms = calculate_processing_time(llm_start)
            log_llm_event("error", error=str(e))
            return None

    def handle_fallback(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle fallback when no other method succeeds.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Fallback response
        """
        source_chain.append("fallback")
        metadata.add_source("fallback")

        # Create fallback result
        fallback_result = response_builder.build_fallback_response(
            FALLBACK_INTENT, source_chain, message
        )

        # Try clarifier for fallback if enabled
        if self.orchestrator.enable_clarifier and self.orchestrator.clarifier:
            clarifier_start = time.perf_counter()
            metadata.clarifier_triggered = True

            try:
                # Increment clarifier rounds in session state
                self.orchestrator._increment_clarifier_rounds(tenant_id, user_id)

                clarification_result = self.orchestrator.clarifier.get_clarification(
                    fallback_result, session_id, tenant_id
                )

                metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)

                # Update response with clarifier metadata
                final_result = response_builder.update_with_clarifier_metadata(
                    fallback_result, clarification_result
                )

                # Don't cache clarification prompts
                if not clarification_result.clarification_needed:
                    self.orchestrator.query_cache.set(message, final_result, tenant_id)

                return final_result

            except Exception as e:
                metadata.add_error(f"Clarifier flow failed: {str(e)}")
                metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)

        else:
            # Clarifier disabled - return fallback result
            self.orchestrator.query_cache.set(message, fallback_result, tenant_id)

        log_parsing_result("fallback", FALLBACK_INTENT, 0.0)
        return fallback_result

    def _handle_clarifier_flow(
        self,
        initial_result: Dict[str, Any],
        session_id: Optional[str],
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
    ) -> Dict[str, Any]:
        """Handle clarifier flow for low-confidence results."""
        clarifier_start = time.perf_counter()
        metadata.clarifier_triggered = True

        try:
            # Get clarification from clarifier
            clarification_result = self.orchestrator.clarifier.get_clarification(
                initial_result, session_id, tenant_id
            )

            metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)

            # Update response with clarifier metadata
            final_result = response_builder.update_with_clarifier_metadata(
                initial_result, clarification_result
            )

            # Log clarifier events
            if clarification_result.max_rounds_reached:
                log_clarifier_event("max_rounds", session_id=session_id)
            elif clarification_result.clarification_needed:
                log_clarifier_event(
                    "prompt_generated",
                    session_id=session_id,
                    round=clarification_result.clarification_round,
                )
            else:
                log_clarifier_event("not_needed", session_id=session_id)

            # Don't cache clarification prompts
            if not clarification_result.clarification_needed:
                self.orchestrator.query_cache.set(
                    initial_result.get("original_message", ""), final_result, tenant_id
                )

            return final_result

        except Exception as e:
            metadata.add_error(f"Clarifier flow failed: {str(e)}")
            metadata.clarifier_time_ms = calculate_processing_time(clarifier_start)
            return initial_result
