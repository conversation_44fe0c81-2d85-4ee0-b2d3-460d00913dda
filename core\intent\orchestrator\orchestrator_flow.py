"""
Flow management for intent parsing orchestration.

This module contains the main flow coordination logic that orchestrates
the individual steps using the modular step implementations.
"""

import logging
from typing import Dict, Optional, Any, List
from ..intent_metadata import ParseMetadata
from ..response import ParseResponseBuilder

from .flow_steps import FlowStepCoordinator
from .flow_fallback import FallbackCoordinator

logger = logging.getLogger(__name__)


class OrchestrationFlowManager:
    """Manages the step-by-step flow of intent parsing orchestration."""

    def __init__(self, orchestrator):
        """Initialize with reference to main orchestrator."""
        self.orchestrator = orchestrator

        # Initialize step coordinator
        self.step_coordinator = FlowStepCoordinator(orchestrator)

        # Initialize fallback coordinator
        self.fallback_coordinator = FallbackCoordinator(orchestrator)

    def try_rule_engine(
        self,
        message: str,
        tenant_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Try to match intent using rule engine.

        Args:
            message: User message
            tenant_id: Tenant identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Rule match result or None if no match
        """
        try:
            return self.step_coordinator.execute_rule_step(
                message, tenant_id, locale, metadata, response_builder, source_chain
            )
        except Exception as e:
            # Attempt error recovery
            recovery_result = self.fallback_coordinator.recover_from_rule_failure(
                message, tenant_id, e
            )
            if recovery_result:
                return recovery_result

            # Recovery failed, log and continue
            logger.error(f"Rule engine step failed with no recovery: {e}")
            return None

    def try_cache(
        self,
        message: str,
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
    ) -> Optional[Dict[str, Any]]:
        """
        Try to get cached result.

        Args:
            message: User message
            tenant_id: Tenant identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking

        Returns:
            Cached result or None if no cache hit
        """
        try:
            return self.step_coordinator.execute_cache_step(
                message, tenant_id, metadata, response_builder, source_chain
            )
        except Exception as e:
            # Attempt error recovery
            recovery_result = self.fallback_coordinator.recover_from_cache_failure(
                message, tenant_id, e
            )
            if recovery_result:
                return recovery_result

            # Recovery failed, log and continue (cache failures are not critical)
            logger.warning(f"Cache step failed with no recovery: {e}")
            return None

    def try_llm_parser(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        locale: str,
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
        session_context: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Try LLM parser for intent detection.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            locale: Language locale
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking
            user_id: User identifier for session state tracking
            session_context: Session context for conversation history

        Returns:
            LLM parsing result or None if LLM disabled/failed
        """
        try:
            result = self.step_coordinator.execute_llm_step(
                message, tenant_id, session_id, locale, metadata,
                response_builder, source_chain, user_id, session_context
            )

            # Handle clarifier if result needs clarification
            if result and result.get("clarification_needed", False):
                if self.orchestrator.enable_clarifier and self.orchestrator.clarifier:
                    # Increment clarifier rounds in session state
                    self.orchestrator._increment_clarifier_rounds(tenant_id, user_id)
                    return self._handle_clarifier_flow(
                        result, session_id, tenant_id, metadata, response_builder
                    )

            return result

        except Exception as e:
            # Attempt error recovery
            recovery_result = self.fallback_coordinator.recover_from_llm_failure(
                message, tenant_id, e
            )
            if recovery_result:
                return recovery_result

            # Recovery failed, log and continue
            logger.error(f"LLM step failed with no recovery: {e}")
            return None

    def handle_fallback(
        self,
        message: str,
        tenant_id: Optional[str],
        session_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
        source_chain: List[str],
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle fallback when no other method succeeds.

        Args:
            message: User message
            tenant_id: Tenant identifier
            session_id: Session identifier
            metadata: Parse metadata for tracking
            response_builder: Response builder instance
            source_chain: Source chain for tracking
            user_id: User identifier for session state tracking

        Returns:
            Fallback response
        """
        return self.fallback_coordinator.handle_fallback(
            message, tenant_id, session_id, metadata,
            response_builder, source_chain, user_id
        )

    def _handle_clarifier_flow(
        self,
        initial_result: Dict[str, Any],
        session_id: Optional[str],
        tenant_id: Optional[str],
        metadata: ParseMetadata,
        response_builder: ParseResponseBuilder,
    ) -> Dict[str, Any]:
        """Handle clarifier flow for low-confidence results."""
        try:
            return self.step_coordinator.execute_clarifier_step(
                initial_result, session_id, tenant_id, metadata, response_builder
            )
        except Exception as e:
            # Attempt error recovery
            recovery_result = self.fallback_coordinator.recover_from_clarifier_failure(
                initial_result.get("original_message", ""), tenant_id, session_id, e
            )
            if recovery_result:
                return recovery_result

            # Recovery failed, return original result
            logger.error(f"Clarifier step failed with no recovery: {e}")
            return initial_result
