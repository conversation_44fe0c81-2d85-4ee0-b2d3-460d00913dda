"""
Utility functions for metadata operations in intent parsing.

This module provides helper functions for metadata creation, manipulation,
and analysis to support the intent parsing system.
"""

import logging
from typing import Dict, Optional, Any, List
from .intent_metadata import ParseMetadata
from .performance_tracker import get_performance_tracker

logger = logging.getLogger(__name__)


def create_parse_metadata(
    message: str,
    tenant_id: Optional[str] = None,
    session_id: Optional[str] = None,
    locale: str = "en",
) -> ParseMetadata:
    """
    Create a new parse metadata instance.

    Args:
        message: User message being parsed
        tenant_id: Tenant identifier
        session_id: Session identifier
        locale: Language locale

    Returns:
        New ParseMetadata instance
    """
    return ParseMetadata(
        message=message, tenant_id=tenant_id, session_id=session_id, locale=locale
    )


def finalize_metadata(metadata: ParseMetadata) -> None:
    """
    Finalize metadata and record performance statistics.
    
    Args:
        metadata: Parse metadata to finalize
    """
    # Finalize timing calculations
    metadata.finalize()
    
    # Record in performance tracker
    performance_tracker = get_performance_tracker()
    performance_tracker.record_request(
        processing_time_ms=metadata.processing_time_ms,
        final_source=metadata.final_source,
        tenant_id=metadata.tenant_id,
        tenant_vertical=metadata.tenant_vertical,
        custom_intents_count=metadata.custom_intents_count,
        rule_time_ms=metadata.rule_check_time_ms,
        cache_time_ms=metadata.cache_check_time_ms,
        llm_time_ms=metadata.llm_call_time_ms,
        clarifier_time_ms=metadata.clarifier_time_ms,
        cache_hit=metadata.cache_hit,
        rule_matched=metadata.rule_matched,
        llm_called=metadata.llm_called,
        clarifier_triggered=metadata.clarifier_triggered,
        source_chain=metadata.source_chain,
    )


def log_performance_summary(metadata: ParseMetadata) -> None:
    """
    Log a performance summary for a completed parse operation.

    Args:
        metadata: Completed parse metadata
    """
    logger.info(
        f"Parse completed: {metadata.final_source} source, "
        f"{metadata.processing_time_ms:.2f}ms total, "
        f"chain: {' → '.join(metadata.source_chain)}, "
        f"tenant: {metadata.tenant_id} ({metadata.tenant_vertical})"
    )


def analyze_metadata_patterns(
    metadata_list: List[ParseMetadata]
) -> Dict[str, Any]:
    """
    Analyze patterns in a list of metadata instances.
    
    Args:
        metadata_list: List of parse metadata instances
        
    Returns:
        Dictionary with pattern analysis results
    """
    if not metadata_list:
        return {"error": "No metadata provided for analysis"}
    
    # Basic statistics
    total_requests = len(metadata_list)
    total_time = sum(m.processing_time_ms for m in metadata_list)
    avg_time = total_time / total_requests if total_requests > 0 else 0
    
    # Source distribution
    source_counts = {}
    for metadata in metadata_list:
        source = metadata.final_source
        source_counts[source] = source_counts.get(source, 0) + 1
    
    # Error analysis
    error_count = sum(1 for m in metadata_list if m.errors)
    warning_count = sum(1 for m in metadata_list if m.warnings)
    
    # Tenant analysis
    tenant_counts = {}
    for metadata in metadata_list:
        tenant = metadata.tenant_id or "default"
        tenant_counts[tenant] = tenant_counts.get(tenant, 0) + 1
    
    # Performance outliers (requests taking > 2x average time)
    outlier_threshold = avg_time * 2 if avg_time > 0 else 1000
    outliers = [
        {
            "processing_time_ms": m.processing_time_ms,
            "final_source": m.final_source,
            "tenant_id": m.tenant_id,
            "errors": m.errors,
        }
        for m in metadata_list 
        if m.processing_time_ms > outlier_threshold
    ]
    
    return {
        "summary": {
            "total_requests": total_requests,
            "total_time_ms": total_time,
            "average_time_ms": avg_time,
            "error_count": error_count,
            "warning_count": warning_count,
        },
        "source_distribution": source_counts,
        "tenant_distribution": tenant_counts,
        "performance_outliers": outliers,
        "outlier_threshold_ms": outlier_threshold,
    }


def extract_debug_info(metadata: ParseMetadata) -> Dict[str, Any]:
    """
    Extract debug information from metadata.
    
    Args:
        metadata: Parse metadata instance
        
    Returns:
        Dictionary with debug information
    """
    return {
        "request_info": {
            "message_length": len(metadata.message),
            "tenant_id": metadata.tenant_id,
            "session_id": metadata.session_id,
            "locale": metadata.locale,
        },
        "timing_info": {
            "total_time_ms": metadata.processing_time_ms,
            "rule_time_ms": metadata.rule_check_time_ms,
            "cache_time_ms": metadata.cache_check_time_ms,
            "llm_time_ms": metadata.llm_call_time_ms,
            "clarifier_time_ms": metadata.clarifier_time_ms,
        },
        "flow_info": {
            "source_chain": metadata.source_chain,
            "final_source": metadata.final_source,
            "rule_matched": metadata.rule_matched,
            "cache_hit": metadata.cache_hit,
            "llm_called": metadata.llm_called,
            "clarifier_triggered": metadata.clarifier_triggered,
        },
        "tenant_info": {
            "vertical": metadata.tenant_vertical,
            "custom_intents_count": metadata.custom_intents_count,
        },
        "issues": {
            "errors": metadata.errors,
            "warnings": metadata.warnings,
        },
        "enhanced_metrics": {
            "tokens_used": metadata.tokens_used,
            "model_used": metadata.model_used,
            "intent_confidence": metadata.intent_confidence,
            "cache_hit_ratio": metadata.cache_hit_ratio,
        }
    }


def compare_metadata(
    metadata1: ParseMetadata, 
    metadata2: ParseMetadata
) -> Dict[str, Any]:
    """
    Compare two metadata instances for analysis.
    
    Args:
        metadata1: First metadata instance
        metadata2: Second metadata instance
        
    Returns:
        Dictionary with comparison results
    """
    return {
        "timing_comparison": {
            "total_time_diff_ms": metadata2.processing_time_ms - metadata1.processing_time_ms,
            "rule_time_diff_ms": metadata2.rule_check_time_ms - metadata1.rule_check_time_ms,
            "cache_time_diff_ms": metadata2.cache_check_time_ms - metadata1.cache_check_time_ms,
            "llm_time_diff_ms": metadata2.llm_call_time_ms - metadata1.llm_call_time_ms,
            "clarifier_time_diff_ms": metadata2.clarifier_time_ms - metadata1.clarifier_time_ms,
        },
        "flow_comparison": {
            "source_chain_1": metadata1.source_chain,
            "source_chain_2": metadata2.source_chain,
            "final_source_1": metadata1.final_source,
            "final_source_2": metadata2.final_source,
            "same_flow": metadata1.source_chain == metadata2.source_chain,
        },
        "context_comparison": {
            "same_tenant": metadata1.tenant_id == metadata2.tenant_id,
            "same_locale": metadata1.locale == metadata2.locale,
            "message_length_1": len(metadata1.message),
            "message_length_2": len(metadata2.message),
        },
        "issues_comparison": {
            "errors_1": len(metadata1.errors),
            "errors_2": len(metadata2.errors),
            "warnings_1": len(metadata1.warnings),
            "warnings_2": len(metadata2.warnings),
        }
    }


def validate_metadata(metadata: ParseMetadata) -> Dict[str, Any]:
    """
    Validate metadata for completeness and consistency.
    
    Args:
        metadata: Parse metadata to validate
        
    Returns:
        Dictionary with validation results
    """
    validation_result = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "info": {}
    }
    
    # Check required fields
    if not metadata.message:
        validation_result["errors"].append("Message is empty")
        validation_result["valid"] = False
    
    if not metadata.final_source:
        validation_result["errors"].append("Final source not set")
        validation_result["valid"] = False
    
    if not metadata.source_chain:
        validation_result["warnings"].append("Source chain is empty")
    
    # Check timing consistency
    if metadata.processing_time_ms <= 0:
        validation_result["warnings"].append("Processing time is zero or negative")
    
    component_time = (
        metadata.rule_check_time_ms + 
        metadata.cache_check_time_ms + 
        metadata.llm_call_time_ms + 
        metadata.clarifier_time_ms
    )
    
    if component_time > metadata.processing_time_ms * 1.1:  # Allow 10% tolerance
        validation_result["warnings"].append(
            f"Component times ({component_time:.2f}ms) exceed total time ({metadata.processing_time_ms:.2f}ms)"
        )
    
    # Check logical consistency
    if metadata.cache_hit and metadata.cache_check_time_ms <= 0:
        validation_result["warnings"].append("Cache hit reported but no cache time recorded")
    
    if metadata.rule_matched and metadata.rule_check_time_ms <= 0:
        validation_result["warnings"].append("Rule match reported but no rule time recorded")
    
    if metadata.llm_called and metadata.llm_call_time_ms <= 0:
        validation_result["warnings"].append("LLM call reported but no LLM time recorded")
    
    if metadata.clarifier_triggered and metadata.clarifier_time_ms <= 0:
        validation_result["warnings"].append("Clarifier triggered but no clarifier time recorded")
    
    # Add info
    validation_result["info"] = {
        "message_length": len(metadata.message),
        "source_chain_length": len(metadata.source_chain),
        "error_count": len(metadata.errors),
        "warning_count": len(metadata.warnings),
        "total_component_time_ms": component_time,
    }
    
    return validation_result


def aggregate_metadata_stats(
    metadata_list: List[ParseMetadata]
) -> Dict[str, Any]:
    """
    Aggregate statistics from multiple metadata instances.
    
    Args:
        metadata_list: List of parse metadata instances
        
    Returns:
        Dictionary with aggregated statistics
    """
    if not metadata_list:
        return {"error": "No metadata provided"}
    
    total_requests = len(metadata_list)
    
    # Timing aggregation
    total_time = sum(m.processing_time_ms for m in metadata_list)
    total_rule_time = sum(m.rule_check_time_ms for m in metadata_list)
    total_cache_time = sum(m.cache_check_time_ms for m in metadata_list)
    total_llm_time = sum(m.llm_call_time_ms for m in metadata_list)
    total_clarifier_time = sum(m.clarifier_time_ms for m in metadata_list)
    
    # Count aggregation
    rule_matches = sum(1 for m in metadata_list if m.rule_matched)
    cache_hits = sum(1 for m in metadata_list if m.cache_hit)
    llm_calls = sum(1 for m in metadata_list if m.llm_called)
    clarifier_triggers = sum(1 for m in metadata_list if m.clarifier_triggered)
    
    # Error aggregation
    total_errors = sum(len(m.errors) for m in metadata_list)
    total_warnings = sum(len(m.warnings) for m in metadata_list)
    
    return {
        "request_count": total_requests,
        "timing_stats": {
            "total_time_ms": total_time,
            "average_time_ms": total_time / total_requests,
            "total_rule_time_ms": total_rule_time,
            "total_cache_time_ms": total_cache_time,
            "total_llm_time_ms": total_llm_time,
            "total_clarifier_time_ms": total_clarifier_time,
        },
        "success_rates": {
            "rule_match_rate": rule_matches / total_requests,
            "cache_hit_rate": cache_hits / total_requests,
            "llm_usage_rate": llm_calls / total_requests,
            "clarifier_usage_rate": clarifier_triggers / total_requests,
        },
        "error_stats": {
            "total_errors": total_errors,
            "total_warnings": total_warnings,
            "error_rate": total_errors / total_requests,
            "warning_rate": total_warnings / total_requests,
        }
    }
