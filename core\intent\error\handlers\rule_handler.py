"""
Rule engine error handler for intent parsing.

This module provides specialized error handling for rule engine
components and operations.
"""

import logging
from typing import Dict, Optional, Any

from .base_handler import Component<PERSON>rror<PERSON>andler
from ..error_types import ErrorSeverity

logger = logging.getLogger(__name__)


class RuleEngineError<PERSON><PERSON>ler(ComponentErrorHandler):
    """Specialized error handler for rule engine components."""

    def __init__(self, error_collector=None):
        super().__init__("rule_engine", error_collector)

    def handle_rule_loading_error(
        self,
        error: Exception,
        config_path: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle rule loading errors."""
        context = {
            "config_path": config_path,
            "error_type": "rule_loading"
        }
        
        def recovery():
            # Attempt to reload with backup configuration
            logger.info(f"Attempting rule loading recovery for {config_path}")
            # This would be implemented based on specific rule loading logic
            return None
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.HIGH,
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def handle_rule_matching_error(
        self,
        error: Exception,
        message: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle rule matching errors."""
        context = {
            "user_message": message[:100],
            "error_type": "rule_matching"
        }
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            tenant_id=tenant_id,
            user_message=message
        )

    def handle_rule_compilation_error(
        self,
        error: Exception,
        rule_pattern: str,
        rule_id: str,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle rule compilation errors."""
        context = {
            "rule_pattern": rule_pattern[:100],
            "rule_id": rule_id,
            "error_type": "rule_compilation"
        }
        
        def recovery():
            # Attempt to skip invalid rule and continue
            logger.info(f"Skipping invalid rule {rule_id} during recovery")
            return {"action": "skip_rule", "rule_id": rule_id}
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.HIGH,
            recovery_function=recovery,
            tenant_id=tenant_id
        )

    def handle_rule_validation_error(
        self,
        error: Exception,
        rule_data: Dict[str, Any],
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle rule validation errors."""
        context = {
            "rule_data": str(rule_data)[:200],
            "error_type": "rule_validation"
        }
        
        return self.handle_error(
            error=error,
            context=context,
            severity=ErrorSeverity.MEDIUM,
            tenant_id=tenant_id
        )

    def get_rule_error_patterns(self) -> Dict[str, Any]:
        """
        Get common rule error patterns.
        
        Returns:
            Dictionary with rule error pattern analysis
        """
        rule_errors = self.error_collector.get_errors_by_component(self.component_name)
        
        patterns = {
            "loading_errors": 0,
            "matching_errors": 0,
            "compilation_errors": 0,
            "validation_errors": 0,
            "tenant_distribution": {},
            "common_patterns": []
        }
        
        for error in rule_errors:
            error_type = error.context.get("error_type", "unknown")
            
            if error_type == "rule_loading":
                patterns["loading_errors"] += 1
            elif error_type == "rule_matching":
                patterns["matching_errors"] += 1
            elif error_type == "rule_compilation":
                patterns["compilation_errors"] += 1
            elif error_type == "rule_validation":
                patterns["validation_errors"] += 1
            
            # Track tenant distribution
            tenant = error.tenant_id or "default"
            patterns["tenant_distribution"][tenant] = patterns["tenant_distribution"].get(tenant, 0) + 1
        
        # Identify common patterns
        if patterns["compilation_errors"] > 5:
            patterns["common_patterns"].append("High rule compilation failure rate")
        
        if patterns["loading_errors"] > 3:
            patterns["common_patterns"].append("Frequent rule loading issues")
        
        return patterns

    def validate_rule_engine_health(self) -> Dict[str, Any]:
        """
        Validate rule engine specific health.
        
        Returns:
            Dictionary with rule engine health validation
        """
        base_validation = self.validate_component_health()
        rule_patterns = self.get_rule_error_patterns()
        
        # Add rule-specific health checks
        if rule_patterns["loading_errors"] > 3:
            base_validation["warnings"].append("Frequent rule loading failures")
            base_validation["health_score"] -= 10
        
        if rule_patterns["compilation_errors"] > 5:
            base_validation["errors"].append("High rule compilation failure rate")
            base_validation["healthy"] = False
            base_validation["health_score"] -= 25
        
        base_validation["rule_patterns"] = rule_patterns
        base_validation["health_score"] = max(base_validation["health_score"], 0)
        
        return base_validation
