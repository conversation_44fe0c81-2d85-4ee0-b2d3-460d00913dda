"""
Core intent clarifier implementation.

This module contains the main IntentClarifier class that orchestrates
the clarification process.
"""

import time
import logging
from typing import Dict, Optional, Any
from clients.redis_client import RedisClient
import config.settings as settings
from .clarifier_models import ClarificationResult, ClarificationSession
from .clarifier_session import ClarificationSessionManager
from .clarifier_prompt import ClarificationPromptGenerator

logger = logging.getLogger(__name__)


class IntentClarifier:
    """
    Intent clarifier for handling low-confidence intent parsing results.

    Features:
    - Session-based clarification tracking (max 2 rounds)
    - Context-aware clarification prompt generation
    - Tenant-specific clarification templates
    - Multi-language support (en, hi, hi-en)
    - Redis-based session state management
    - Integration with Azure OpenAI for prompt generation
    """

    def __init__(
        self,
        redis_client: Optional[RedisClient] = None,
        confidence_threshold: float = 0.80,
        max_rounds: int = 2,
        session_ttl: int = 1800,
    ):
        """
        Initialize the intent clarifier.

        Args:
            redis_client: Redis client for session storage
            confidence_threshold: Threshold below which clarification is needed
            max_rounds: Maximum clarification rounds per session
            session_ttl: Session TTL in seconds (default: 30 minutes)
        """
        self.redis_client = redis_client or RedisClient(
            host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=settings.REDIS_DB
        )
        self.confidence_threshold = confidence_threshold
        self.max_rounds = max_rounds
        self.session_ttl = session_ttl

        # Initialize components
        self.session_manager = ClarificationSessionManager(
            self.redis_client, session_ttl
        )
        self.prompt_generator = ClarificationPromptGenerator()

        # Performance tracking
        self._performance_stats = {
            "total_clarifications": 0,
            "prompts_generated": 0,
            "sessions_created": 0,
            "sessions_completed": 0,
            "max_rounds_reached": 0,
            "total_time_ms": 0.0,
        }

    def should_clarify(self, intent_result: Dict[str, Any]) -> bool:
        """
        Determine if clarification is needed based on intent result.

        Args:
            intent_result: Intent parsing result from intent_parser

        Returns:
            True if clarification is needed, False otherwise
        """
        confidence = intent_result.get("confidence", 0.0)
        source = intent_result.get("source", "")

        # Clarify if confidence is below threshold
        if confidence < self.confidence_threshold:
            return True

        # Clarify if LLM marked it as needing clarification
        if intent_result.get("clarification_needed", False):
            return True

        # Clarify if intent is 'Unknown' or 'error'
        intent = intent_result.get("intent", "")
        if intent.lower() in ["unknown", "error"]:
            return True

        return False

    def get_clarification(
        self,
        intent_result: Dict[str, Any],
        session_id: str,
        tenant_id: Optional[str] = None,
    ) -> ClarificationResult:
        """
        Get clarification prompt for low-confidence intent result.

        Args:
            intent_result: Intent parsing result from intent_parser
            session_id: Session identifier
            tenant_id: Tenant identifier

        Returns:
            ClarificationResult with prompt and session state
        """
        start_time = time.perf_counter()

        try:
            # Check if clarification is needed
            if not self.should_clarify(intent_result):
                return ClarificationResult(clarification_needed=False)

            # Load or create session
            session = self.session_manager.load_session_state(session_id, tenant_id)

            if not session:
                # Create new session
                session = self.session_manager.create_session(
                    session_id=session_id,
                    tenant_id=tenant_id,
                    original_message=intent_result.get("original_message", ""),
                    original_intent=intent_result.get("intent", "Unknown"),
                    original_confidence=intent_result.get("confidence", 0.0),
                    max_rounds=self.max_rounds,
                )
                self._performance_stats["sessions_created"] += 1

            # Check if max rounds reached
            if session.clarification_round >= session.max_rounds:
                # Proper Redis session cleanup for "clarifier:session:*" keys when max rounds reached - FIXED
                logger.info(f"Max clarification rounds ({session.max_rounds}) reached for session {session_id}")

                # Clean up session from Redis
                cleanup_success = self.session_manager.complete_session(session_id, tenant_id)
                if cleanup_success:
                    logger.debug(f"Successfully cleaned up clarifier session {session_id}")
                else:
                    logger.warning(f"Failed to clean up clarifier session {session_id}")

                # Also clean up any related session state
                self._cleanup_related_session_state(session_id, tenant_id)

                return ClarificationResult(
                    clarification_needed=False,
                    max_rounds_reached=True,
                    session_state=session,
                )

            # Generate clarification prompt
            clarification_prompt = self.prompt_generator.generate_clarification_prompt(
                intent_result, session, tenant_id
            )

            if not clarification_prompt:
                logger.warning("Failed to generate clarification prompt")
                return ClarificationResult(clarification_needed=False)

            # Get suggested intents
            suggested_intents = self.prompt_generator.get_suggested_intents(
                intent_result, tenant_id
            )

            # Update session state
            session.clarification_round += 1
            self.session_manager.save_session_state(session)

            # Update performance stats
            processing_time = (time.perf_counter() - start_time) * 1000
            self._performance_stats["total_clarifications"] += 1
            self._performance_stats["prompts_generated"] += 1
            self._performance_stats["total_time_ms"] += processing_time

            return ClarificationResult(
                clarification_needed=True,
                clarification_prompt=clarification_prompt,
                clarification_round=session.clarification_round,
                max_rounds_reached=False,
                session_state=session,
                suggested_intents=suggested_intents,
            )

        except Exception as e:
            logger.error(f"Error during clarification: {e}")
            processing_time = (time.perf_counter() - start_time) * 1000
            self._performance_stats["total_time_ms"] += processing_time
            return ClarificationResult(clarification_needed=False)

    def complete_session(
        self, session_id: str, tenant_id: Optional[str] = None
    ) -> bool:
        """
        Mark a clarification session as completed and clean up.

        Args:
            session_id: Session identifier
            tenant_id: Tenant identifier

        Returns:
            True if session was found and completed, False otherwise
        """
        result = self.session_manager.complete_session(session_id, tenant_id)
        if result:
            self._performance_stats["sessions_completed"] += 1
            # Clean up related session state on successful completion
            self._cleanup_related_session_state(session_id, tenant_id)
            logger.debug(f"Clarifier session {session_id} completed successfully")
        return result

    def _cleanup_related_session_state(self, session_id: str, tenant_id: Optional[str] = None) -> None:
        """
        Clean up related session state when clarifier exits.

        Args:
            session_id: Session identifier
            tenant_id: Tenant identifier
        """
        try:
            # Clean up any memory session state related to clarifier
            from core.session.session_state import SessionStore
            from clients.redis_client import RedisClient

            redis_client = RedisClient()
            session_store = SessionStore(redis_client)

            # Reset clarifier rounds in session context
            if session_id and tenant_id:
                context = session_store.get_context(tenant_id, session_id)
                if context and "clarifier_rounds" in context:
                    context["clarifier_rounds"] = 0
                    session_store.save_context(tenant_id, session_id, context)
                    logger.debug(f"Reset clarifier rounds for session {session_id}")

            # Clean up any orphaned clarifier keys
            pattern = f"clarifier:session:*{session_id}*"
            try:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted_count = self.redis_client.delete(*keys)
                    logger.debug(f"Cleaned up {deleted_count} orphaned clarifier keys for session {session_id}")
            except Exception as e:
                logger.warning(f"Failed to clean up orphaned clarifier keys: {e}")

        except Exception as e:
            logger.warning(f"Failed to cleanup related session state for {session_id}: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get clarifier performance statistics."""
        return self._performance_stats.copy()

    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on clarifier components.

        Returns:
            Dictionary with health status
        """
        try:
            # Test Redis connection
            redis_working = False
            try:
                self.redis_client.ping()
                redis_working = True
            except Exception as e:
                logger.warning(f"Redis health check failed: {e}")

            # Test LLM connection
            llm_test = self.prompt_generator.test_llm_connection()

            return {
                "status": (
                    "healthy"
                    if redis_working and llm_test.get("model_available")
                    else "degraded"
                ),
                "redis_connected": redis_working,
                "openai_connected": llm_test.get("model_available", False),
                "confidence_threshold": self.confidence_threshold,
                "max_rounds": self.max_rounds,
                "llm_response_time_ms": llm_test.get("response_time_ms", 0),
                "timestamp": time.time(),
            }

        except Exception as e:
            return {"status": "unhealthy", "error": str(e), "timestamp": time.time()}
