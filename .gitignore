# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.pdb
*.egg
*.egg-info/
dist/
build/

# Virtual environments
venv/
env/
ENV/
.venv/

# Jupyter notebooks
.ipynb_checkpoints

# Logs
*.log
logs/

# Environments and secrets
.env
.env.*

# OS files
.DS_Store
Thumbs.db

# MyPy, Pytest, Coverage
.mypy_cache/
.pytest_cache/
.coverage
htmlcov/

# IDE/editor configs
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Docker
*.pid

# Redis dump
dump.rdb

# Database / migrations (if using SQLite or local dev db)
*.db
*.sqlite3

# Node (if any frontend or tooling)
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# CI/CD or build artifacts
*.tar.gz
*.whl
.eggs/
MANIFEST

# Other temporary files
*.bak
*.swp
*.tmp
*.orig
