# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
**/node_modulesgit rm -r --cached backend/node_modules
/.pnp
.pnp.js



# Tailwind CSS generated files
public/css/tailwind.css

# Next.js build output
.next/
out/
/dist

# Editor directories and files
.vscode/
.idea/
*.sublime-workspace


# testing
/coverage

# production
/build

# misc
.DS_Store
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

*storybook.log

package-lock.json
