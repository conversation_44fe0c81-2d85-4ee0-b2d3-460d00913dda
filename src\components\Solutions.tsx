
import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, Users, BarChart3, Bot, Truck, Search } from 'lucide-react';

const solutionItems = [
  {
    icon: <ShoppingCart className="h-6 w-6 text-purple-600" />,
    title: "Product Discovery AI",
    description: "Intelligent product search and recommendations that understand customer intent and preferences in real-time.",
    features: ["Natural language search", "Visual product matching", "Personalized recommendations", "Cross-selling opportunities"]
  },
  {
    icon: <Users className="h-6 w-6 text-purple-600" />,
    title: "Customer Service AI",
    description: "24/7 support agents that handle inquiries, process returns, and provide detailed product information.",
    features: ["Instant response to FAQs", "Order status tracking", "Return processing", "Product specifications"]
  },
  {
    icon: <BarChart3 className="h-6 w-6 text-purple-600" />,
    title: "Inventory Intelligence",
    description: "Predictive analytics for inventory management, reducing stockouts and optimizing warehouse operations.",
    features: ["Demand forecasting", "Inventory level alerts", "Reorder recommendations", "Seasonal trend analysis"]
  },
  {
    icon: <Bot className="h-6 w-6 text-purple-600" />,
    title: "Personalization Engine",
    description: "Tailored shopping experiences through AI that learns from customer behavior and preferences.",
    features: ["Customer segmentation", "Behavioral analysis", "Personalized marketing", "Individual preferences"]
  },
  {
    icon: <Truck className="h-6 w-6 text-purple-600" />,
    title: "Logistics Optimization",
    description: "Intelligent routing and delivery planning that reduces costs and improves customer satisfaction.",
    features: ["Route optimization", "Delivery time prediction", "Carrier selection", "Returns management"]
  },
  {
    icon: <Search className="h-6 w-6 text-purple-600" />,
    title: "Market Intelligence",
    description: "Competitive analysis and market trend insights to inform product strategy and pricing.",
    features: ["Competitor monitoring", "Price optimization", "Trend detection", "Consumer sentiment analysis"]
  }
];

const Solutions = () => {
  return (
    <section id="solutions" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Vertical AI solutions for every retail challenge
          </h2>
          <p className="text-lg text-gray-600">
            Our specialized AI agents are designed specifically for retail and e-commerce operations,
            with deep understanding of industry challenges and opportunities.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {solutionItems.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white border border-gray-100 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center mb-6">
                {item.icon}
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{item.title}</h3>
              <p className="text-gray-600 mb-6">{item.description}</p>
              
              <ul className="space-y-2">
                {item.features.map((feature, i) => (
                  <li key={i} className="flex items-center text-sm text-gray-600">
                    <svg className="h-4 w-4 text-purple-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Solutions;
